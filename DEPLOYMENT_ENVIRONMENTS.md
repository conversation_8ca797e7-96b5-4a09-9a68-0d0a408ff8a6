# Multi-Environment Deployment Guide

This guide explains how to deploy the CS AI App to different environments (dev, stg, prod) using environment-specific configuration files.

## Overview

The deployment system supports multiple environments by using different `.env` files. Each environment can have its own:

- API endpoints
- Database connections
- Port configurations
- Service credentials
- Debug settings

## Environment Files

### Default Environment Files

- **`.env`** - Production environment (default)
- **`.env.stg`** - Staging environment
- **`.env.dev`** - Development environment

### Environment File Structure

Each environment file should contain:

```bash
# Environment identifier
APP_ENVIRONMENT=dev|stg|prod

# Application URLs
NEXT_PUBLIC_APP_BASE_URL=http://localhost:3000
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000

# External service configurations
WAHA_API_URL=https://waha-dev.example.com
WAHA_API_KEY=your_api_key_here
GOWA_API_BASE=https://gowa-dev.example.com
GOWA_USERNAME=your_username
GOWA_PASSWORD=your_password

# Database configuration
MONGODB_URI=mongodb://localhost:27017/cs-ai-dev
MONGODB_DB_NAME=cs-ai-dev

# Docker port mapping
DOCKER_EXPOSED_PORT=3004

# Environment-specific settings
LOG_LEVEL=debug
DEBUG_MODE=true
```

## Deployment Commands

### Using Makefile

#### Deploy to Specific Environment

```bash
# Deploy to stg
make deploy ENV=.env.stg

# Deploy to dev
make deploy ENV=.env.dev

# Deploy to prod (default)
make deploy ENV=.env
# or simply
make deploy
```

#### Deploy Specific Image Tag to Environment

```bash
# Deploy specific version to stg
make deploy TAG=cs-ai-app:v1.2.3 ENV=.env.stg

# Deploy specific version to dev
make deploy TAG=cs-ai-app:v1.2.3 ENV=.env.dev
```

#### Development Deployment (with volume mounts)

```bash
# Deploy for dev with hot reloading
make deploy_dev ENV=.env.dev
```

#### Build and Deploy

```bash
# Build new image and deploy to stg
make redeploy ENV=.env.stg

# Build new image and deploy to dev
make redeploy ENV=.env.dev
```

#### Rollback

```bash
# Rollback stg to previous version
make rollback TAG=cs-ai-app:v1.2.2 ENV=.env.stg

# Rollback dev to previous version
make rollback TAG=cs-ai-app:v1.2.2 ENV=.env.dev
```

### Using docker-start.sh Script

#### Deploy with Environment File

```bash
# Deploy to stg in detached mode
./docker-start.sh -e .env.stg -d

# Deploy to dev in detached mode
./docker-start.sh -e .env.dev -d

# Deploy specific tag to stg
./docker-start.sh -e .env.stg -t cs-ai-app:v1.2.3 -d

# Build and deploy to dev
./docker-start.sh -e .env.dev -b -d
```

## Management Commands

### Check Status

```bash
# Check stg deployment status
make show_status ENV=.env.stg

# Check dev deployment status
make show_status ENV=.env.dev
```

### View Logs

```bash
# View stg logs
make show_logs ENV=.env.stg

# View dev logs
make show_logs ENV=.env.dev
```

## Port Configuration

Each environment should use a different port to avoid conflicts:

- **Production**: Port 3002 (default)
- **Staging**: Port 3003
- **Development**: Port 3004

Configure the port in each environment file:

```bash
DOCKER_EXPOSED_PORT=3003  # for stg
DOCKER_EXPOSED_PORT=3004  # for dev
```

## Best Practices

### 1. Environment Separation

- Use different databases for each environment
- Use different API keys and credentials
- Use different external service endpoints

### 2. Configuration Management

- Keep sensitive data in environment files (not in code)
- Use `.env.example` as a template for new environments
- Document required environment variables

### 3. Deployment Workflow

```bash
# Development workflow
make deploy_dev ENV=.env.dev

# Staging deployment
make deploy TAG=cs-ai-app:v1.2.3 ENV=.env.stg

# Production deployment
make deploy TAG=cs-ai-app:v1.2.3 ENV=.env
```

### 4. Monitoring

```bash
# Check all environments
make show_status ENV=.env.dev
make show_status ENV=.env.stg
make show_status ENV=.env
```

## Troubleshooting

### Environment File Not Found

```bash
[ERROR] Environment file .env.stg not found!
Available .env files:
-rw-r--r-- 1 <USER> <GROUP> 1234 Sep 26 18:00 .env
-rw-r--r-- 1 <USER> <GROUP> 1234 Sep 26 18:00 .env.dev
```

**Solution**: Create the missing environment file or check the file path.

### Port Conflicts

```bash
Error: Bind for 0.0.0.0:3003 failed: port is already allocated
```

**Solution**:

1. Stop the conflicting container: `docker stop <container_name>`
2. Or change the port in the environment file
3. Or use a different environment file

### Image Not Found

```bash
[ERROR] Image cs-ai-app:v1.2.3 not found!
```

**Solution**:

1. Build the image: `make build_image`
2. Or use an existing image: `make list_images`
3. Or pull from registry if available

## Examples

### Complete Development Setup

```bash
# 1. Create dev environment file
cp .env.example .env.dev

# 2. Edit dev settings
nano .env.dev

# 3. Deploy for dev
make deploy_dev ENV=.env.dev

# 4. Check status
make show_status ENV=.env.dev
```

### Staging Deployment

```bash
# 1. Build new version
make build_image

# 2. Deploy to stg
make deploy TAG=cs-ai-app:latest ENV=.env.stg

# 3. Verify deployment
make show_status ENV=.env.stg
```

### Production Release

```bash
# 1. Test in stg first
make deploy TAG=cs-ai-app:v1.2.3 ENV=.env.stg

# 2. Deploy to prod
make deploy TAG=cs-ai-app:v1.2.3 ENV=.env

# 3. Monitor deployment
make show_logs ENV=.env
```
