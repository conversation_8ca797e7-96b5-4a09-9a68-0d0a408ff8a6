"use client"

import { Edit, Trash2, Phone, Mail, Tag, Calendar, User } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import { DisplayVariant } from "@/components/crud-page"
import { Contact } from "@/lib/repositories/contacts"

interface ContactCardProps {
  item: Contact
  variant: DisplayVariant
  onEdit?: (id: string) => void
  onDelete?: (id: string) => void
}

export default function ContactCard({
  item,
  variant,
  onEdit,
  onDelete,
}: ContactCardProps) {
  const { t } = useLocalization("contacts", locales)

  const cardClass =
    variant === "GRID"
      ? "bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow"
      : "bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const renderTags = (tags: string[]) => {
    if (!tags || tags.length === 0) return null

    const displayTags = variant === "GRID" ? tags.slice(0, 2) : tags

    return (
      <div className="flex flex-wrap gap-1">
        {displayTags.map((tag, index) => (
          <span
            key={index}
            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
          >
            <Tag className="w-3 h-3 mr-1" />
            {tag}
          </span>
        ))}
        {variant === "GRID" && tags.length > 2 && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
            +{tags.length - 2}
          </span>
        )}
      </div>
    )
  }

  if (variant === "GRID") {
    return (
      <div className={cardClass}>
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900 truncate">
              {item.name}
            </h3>
            {item.phone && (
              <div className="flex items-center text-xs text-gray-600 mt-1">
                <Phone className="w-3 h-3 mr-1" />
                {item.phone}
              </div>
            )}
          </div>
        </div>

        {/* Email */}
        {item.email && (
          <div className="flex items-center text-xs text-gray-600 mb-2">
            <Mail className="w-3 h-3 mr-1" />
            <span className="truncate">{item.email}</span>
          </div>
        )}

        {/* Tags */}
        {item.tags && item.tags.length > 0 && (
          <div className="mb-3">{renderTags(item.tags)}</div>
        )}

        {/* Notes count */}
        {item.notes && item.notes.length > 0 && (
          <div className="text-xs text-gray-500 mb-3">
            {item.notes.length} {t("notes")}
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-2 pt-2 border-t border-gray-100">
          {onEdit && (
            <button
              onClick={() => onEdit(item.id)}
              className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded transition-colors"
              title={t("edit_contact")}
            >
              <Edit className="w-3 h-3" />
            </button>
          )}
          {onDelete && (
            <button
              onClick={() => onDelete(item.id)}
              className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
              title={t("delete_contact")}
            >
              <Trash2 className="w-3 h-3" />
            </button>
          )}
        </div>
      </div>
    )
  }

  // Card view (detailed)
  return (
    <div className={cardClass}>
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {item.name}
          </h3>
          <div className="flex items-center text-sm text-gray-600">
            <User className="w-4 h-4 mr-1" />
            {t("created_by")} {item.createdBy}
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="space-y-3 mb-4">
        {item.phone && (
          <div className="flex items-center text-sm text-gray-700">
            <Phone className="w-4 h-4 mr-3 text-gray-400" />
            <span>{item.phone}</span>
          </div>
        )}

        {item.email && (
          <div className="flex items-center text-sm text-gray-700">
            <Mail className="w-4 h-4 mr-3 text-gray-400" />
            <span>{item.email}</span>
          </div>
        )}

        {item.notes && item.notes.length > 0 && (
          <div className="flex items-center text-sm text-gray-700">
            <Calendar className="w-4 h-4 mr-3 text-gray-400" />
            <span>
              {item.notes.length} {t("notes")}
            </span>
          </div>
        )}
      </div>

      {/* Tags */}
      {item.tags && item.tags.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">
            {t("tags")}:
          </h4>
          {renderTags(item.tags)}
        </div>
      )}

      {/* Dates */}
      <div className="grid grid-cols-2 gap-4 mb-4 text-xs text-gray-500">
        <div>
          <span className="font-medium">{t("headers.created_date")}:</span>
          <br />
          {formatDate(item.createdAt.toISOString())}
        </div>
        <div>
          <span className="font-medium">{t("headers.updated_date")}:</span>
          <br />
          {formatDate(item.updatedAt.toISOString())}
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
        {onEdit && (
          <button
            onClick={() => onEdit(item.id)}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors"
          >
            <Edit className="w-4 h-4 mr-2" />
            {t("edit_contact")}
          </button>
        )}
        {onDelete && (
          <button
            onClick={() => onDelete(item.id)}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            {t("delete_contact")}
          </button>
        )}
      </div>
    </div>
  )
}
