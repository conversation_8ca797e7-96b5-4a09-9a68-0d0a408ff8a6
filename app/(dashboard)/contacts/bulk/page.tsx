"use client"

import DataBulkPage from "@/components/crud-page/DataBulkPageSimple"
import type { DataBulkConfig } from "@/components/crud-page/DataBulkPageSimple"
import type { ImportResult } from "@/components/crud-page/BulkImportComponent"
import type { DeleteResult } from "@/components/crud-page/BulkDeleteComponent"
import { ContactsAPI } from "@/lib/services/contactsApi"
import { toast } from "sonner"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"
import {
  Contact,
  ContactCreateInput,
  ContactUpdateInput,
} from "@/lib/repositories/contacts/interface"
import {
  ContactCreateCSVSchema,
  ContactUpdateCSVSchema,
  ContactCreateCSVInput,
  ContactUpdateCSVInput,
} from "@/lib/validations/contact"
import { z } from "zod"

// Create a schema for bulk update that includes the required ID field
const ContactBulkUpdateCSVSchema = ContactUpdateCSVSchema.extend({
  id: z.string().min(1, "ID is required"),
})

export default function ContactsBulkPage() {
  const { t } = useLocalization("contacts", locales)

  // Contacts bulk operations configuration
  const contactsBulkConfig: DataBulkConfig<
    Contact,
    ContactCreateInput,
    ContactUpdateInput,
    ContactCreateCSVInput,
    ContactUpdateCSVInput & { id: string }
  > = {
    title: t("bulk.title"),
    subtitle: t("bulk.subtitle"),
    backRoute: "/contacts",

    // Enable all operations
    enableImport: true,
    enableUpdate: true,
    enableDelete: true,

    // Import configuration
    importConfig: {
      fields: [
        {
          name: "name",
          label: t("fields.name.label"),
          required: true,
          type: "string",
          example: "John Doe",
        },
        {
          name: "phone",
          label: t("fields.phone.label"),
          required: true,
          type: "phone",
          example: "+1234567890",
        },
        {
          name: "email",
          label: t("fields.email.label"),
          required: false,
          type: "email",
          example: "<EMAIL>",
        },
        {
          name: "tags",
          label: t("fields.tags.label"),
          required: false,
          type: "string",
          example: "VIP,Customer",
        },
        {
          name: "company",
          label: t("fields.company.label"),
          required: false,
          type: "string",
          example: "Acme Corp",
        },
        {
          name: "position",
          label: t("fields.position.label"),
          required: false,
          type: "string",
          example: "Manager",
        },
        {
          name: "address",
          label: t("fields.address.label"),
          required: false,
          type: "string",
          example: "123 Main St",
        },
        {
          name: "birthday",
          label: t("fields.birthday.label"),
          required: false,
          type: "date",
          example: "1990-01-15",
        },
        {
          name: "notes",
          label: t("fields.notes.label"),
          required: false,
          type: "string",
          example: "Important customer",
        },
      ],
      maxFileSize: 10,
      maxRecords: 1000,
      supportedFormats: ["csv", "json"],

      generateTemplate: () => {
        // Generate CSV-compatible template data
        const contactsInput = [
          {
            name: "John Doe",
            phone: "+1234567890",
            email: "<EMAIL>",
            tags: "VIP, Customer", // CSV format: comma-separated string
            notes: "Important customer", // CSV format: single string
          },
          {
            name: "Jane Smith",
            phone: "+0987654321",
            email: "<EMAIL>",
            tags: "Lead, Prospect", // CSV format: comma-separated string
            notes: "Potential high-value client", // CSV format: single string
          },
        ]
        return contactsInput
      },

      // Use Zod schema for validation instead of manual validation
      zodSchema: ContactCreateCSVSchema,

      importData: async (data: ContactCreateInput[]): Promise<ImportResult> => {
        try {
          // Data is already transformed by Zod schema from CSV format to API format
          const result = await ContactsAPI.BulkCreate(data).request()

          return {
            total: result.total,
            successful: result.successful,
            failed: result.failed,
            errors: (result.errors || []).map((error) => ({
              row: error.row || 0,
              field: error.field || "general",
              message: error.message,
            })),
          }
        } catch (error: any) {
          console.error(t("errors.bulkImportError"), error)
          return {
            total: data.length,
            successful: 0,
            failed: data.length,
            errors: data.map((_, index) => ({
              row: index,
              field: "general",
              message: error?.message || t("errors.importFailed"),
            })),
          }
        }
      },

      headers: [
        t("bulk.headers.import.0"),
        t("bulk.headers.import.1"),
        t("bulk.headers.import.2"),
        t("bulk.headers.import.3"),
      ],
      transformToTableRow: (item: Record<string, any>) => ({
        id: item.id,
        columns: [
          item.name || "",
          item.phone || "",
          item.email || "",
          item.tags ? item.tags : "",
        ],
      }),
    },

    // Update configuration
    updateConfig: {
      fetchData: async () => {
        try {
          const result = await ContactsAPI.All({
            page: 1,
            per_page: 1000, // Get all contacts for bulk operations
            sort: [],
            filters: [],
          }).request()

          return (
            result.items.map((a) => {
              a.notes = a.notes
                ? a.notes.map((note) => note.text).join(", ")
                : ("" as any)
              return a
            }) || []
          )
        } catch (error) {
          console.error(t("errors.fetchForUpdateFailed"), error)
          toast.error(t("errors.fetchForUpdateFailed"))
          return []
        }
      },

      updateData: async (data) => {
        try {
          const result = await ContactsAPI.BulkUpdate(data).request()
          return {
            total: result.total,
            successful: result.successful,
            failed: result.failed,
            errors: (result.errors || []).map((error) => ({
              row: error.row || 0,
              field: error.field || "general",
              message: error.message,
            })),
          }
        } catch (error: any) {
          console.error(t("errors.updateFailed"), error)
          return {
            total: data.length,
            successful: 0,
            failed: data.length,
            errors: data.map((_, index) => ({
              row: index,
              field: "general",
              message: error?.message || t("errors.updateFailed"),
            })),
          }
        }
      },

      headers: [
        t("headers.name"),
        t("headers.phone"),
        t("headers.email"),
        t("headers.tags"),
        t("headers.created_date"),
      ],
      transformToTableRow: (item: Record<string, any>) => ({
        id: item.id,
        columns: [
          item.name || "",
          item.phone || "",
          item.email || "",
          item.tags ? item.tags.join(", ") : "",
          item.createdAt ? new Date(item.createdAt).toLocaleDateString() : "",
        ],
      }),

      // Template generation for bulk update
      generateTemplate: () => {
        // Generate CSV-compatible template data
        const contactsInput = [
          {
            id: "1",
            name: "John Doe",
            phone: "+1234567890",
            email: "<EMAIL>",
            tags: "VIP, Customer", // CSV format: comma-separated string
            notes: "Important customer", // CSV format: single string
          },
          {
            id: "2",
            name: "Jane Smith",
            phone: "+0987654321",
            email: "<EMAIL>",
            tags: "Lead, Prospect", // CSV format: comma-separated string
            notes: "Potential client", // CSV format: single string
          },
        ]
        return contactsInput
      },

      // Use Zod schema for validation
      zodSchema: ContactBulkUpdateCSVSchema,

      // Transform data for CSV export (remove sensitive fields, format data)
      transformForExport: (data: Record<string, any>[]) => {
        return data.map((contact) => ({
          id: contact.id,
          name: contact.name,
          phone: contact.phone,
          email: contact.email || "",
          tags: Array.isArray(contact.tags)
            ? contact.tags.join(", ")
            : contact.tags || "",
          company: contact.company || "",
          position: contact.position || "",
          address: contact.address || "",
          birthday: contact.birthday || "",
          notes: contact.notes || "",
          // Exclude internal fields like createdAt, updatedAt, organizationId, etc.
        }))
      },

      exportFilename: "contacts_bulk_update.csv",

      importConfig: {
        fields: [
          {
            name: "id",
            label: "ID",
            required: true,
            type: "string",
            example: "123",
          },
          {
            name: "name",
            label: t("fields.name.label"),
            required: true,
            type: "string",
            example: "John Doe",
          },
          {
            name: "phone",
            label: t("fields.phone.label"),
            required: true,
            type: "phone",
            example: "+1234567890",
          },
          {
            name: "email",
            label: t("fields.email.label"),
            required: false,
            type: "email",
            example: "<EMAIL>",
          },
          {
            name: "company",
            label: t("fields.company.label"),
            required: false,
            type: "string",
            example: "Acme Corp",
          },
          {
            name: "tags",
            label: t("fields.tags.label"),
            required: false,
            type: "string",
            example: "VIP,Customer",
          },
        ],
        maxFileSize: 10,
        maxRecords: 1000,
        supportedFormats: ["csv", "json"],
        generateTemplate: () => {
          // Generate CSV-compatible template data
          const updateTemplate = [
            {
              id: "contact-id-1",
              name: "John Doe Updated",
              phone: "+1234567890",
              email: "<EMAIL>",
              tags: "VIP, Updated", // CSV format: comma-separated string
              notes: "Updated customer info", // CSV format: single string
            },
            {
              id: "contact-id-2",
              name: "Jane Smith Updated",
              phone: "+0987654321",
              email: "<EMAIL>",
              tags: "Lead, Updated", // CSV format: comma-separated string
              notes: "Updated contact details", // CSV format: single string
            },
          ]
          return updateTemplate
        },

        // Use Zod schema for validation
        zodSchema: ContactBulkUpdateCSVSchema,

        importData: async (data: ({ id: string } & ContactUpdateInput)[]) => {
          try {
            const result = await ContactsAPI.BulkUpdate(data).request()

            return {
              total: result.total,
              successful: result.successful,
              failed: result.failed,
              errors: (result.errors || []).map((error) => ({
                row: error.row || 0,
                field: error.field || "general",
                message: error.message,
              })),
            }
          } catch (error: any) {
            console.error(t("errors.updateFailed"), error)
            return {
              total: data.length,
              successful: 0,
              failed: data.length,
              errors: data.map((_, index) => ({
                row: index,
                field: "general",
                message: error?.message || t("errors.updateFailed"),
              })),
            }
          }
        },
      },
    },

    // Delete configuration
    deleteConfig: {
      fetchData: async () => {
        try {
          const result = await ContactsAPI.All({
            page: 1,
            per_page: 1000, // Get all contacts for bulk operations
            sort: [],
            filters: [],
          }).request()

          return result.items || []
        } catch (error) {
          console.error(t("errors.fetchForDeleteFailed"), error)
          toast.error(t("errors.fetchForDeleteFailed"))
          return []
        }
      },

      deleteData: async (ids: string[]): Promise<DeleteResult> => {
        try {
          const result = await ContactsAPI.BulkDelete(ids).request()

          return {
            total: result.total,
            successful: result.successful,
            failed: result.failed,
            errors: (result.errors || []).map((error) => ({
              id: error.id || "",
              message: error.message,
            })),
          }
        } catch (error: any) {
          console.error(t("errors.updateFailed"), error)
          return {
            total: ids.length,
            successful: 0,
            failed: ids.length,
            errors: ids.map((id) => ({
              id,
              message: error?.message || t("errors.updateFailed"),
            })),
          }
        }
      },

      headers: [
        t("bulk.headers.delete.0"),
        t("bulk.headers.delete.1"),
        t("bulk.headers.delete.2"),
        t("bulk.headers.delete.3"),
        t("bulk.headers.delete.4"),
      ],
      transformToTableRow: (item: Record<string, any>) => ({
        id: item.id,
        columns: [
          item.name || "",
          item.phone || "",
          item.email || "",
          item.company || "",
          item.createdAt ? new Date(item.createdAt).toLocaleDateString() : "",
        ],
      }),

      itemName: t("bulk.itemName"),
      itemNamePlural: t("bulk.itemNamePlural"),
    },
  }

  return <DataBulkPage config={contactsBulkConfig} />
}
