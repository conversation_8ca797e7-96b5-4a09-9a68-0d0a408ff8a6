{"bulk": {"headers": {"delete": {"0": "Name", "1": "Phone", "2": "Email", "3": "Company", "4": "Created Date"}, "import": {"0": "Name", "1": "Phone", "2": "Email", "3": "Tag"}}, "itemName": "contact", "itemNamePlural": "contacts", "subtitle": "Import, update, and delete contact records in bulk", "title": "Contacts"}, "buttons": {"cancel": "Cancel", "create": "Create Contact", "update": "Update Contact"}, "date_filter_options": {"all": "All Time", "last_month": "Last Month", "last_week": "Last Week", "this_month": "This Month", "this_week": "This Week", "today": "Today", "yesterday": "Yesterday"}, "errors": {"bulkImportError": "Bulk import error", "createError": "Error creating contact", "createFailed": "Failed to create contact", "fetchForDeleteFailed": "Failed to fetch contact", "fetchForUpdateFailed": "Failed to fetch contacts for bulk update", "importFailed": "Import failed", "updateFailed": "Failed to update contact"}, "fields": {"address": {"description": "Physical address or location information for the contact", "examples": "Examples:\n• J<PERSON><PERSON> No. 45, Central Jakarta, Indonesia\n• Jl. <PERSON> 123, Yogyakarta 55271\n• ABC Building 5th Floor, Jl. <PERSON>, Jakarta\n• Indah Housing Complex Block A No. 10", "label": "Address", "placeholder": "Enter full address"}, "birthday": {"description": "Date of birth for personal contacts (optional)", "examples": "Examples: 1990-05-15, 1985-12-25, 1992-03-08", "label": "Birthday", "placeholder": "Select birthday"}, "company": {"description": "Organization or company where the contact works", "examples": "Examples: Microsoft Corporation, PT Telkom Indonesia, ABC Trading Ltd", "label": "Company", "placeholder": "Enter company name"}, "createdAt": {"label": "Created Date"}, "createdBy": {"label": "Created By"}, "email": {"description": "Primary email address for communication and contact identification", "examples": "Examples: <EMAIL>, <EMAIL>, <EMAIL>", "label": "Email Address", "placeholder": "Enter email address"}, "name": {"description": "Full name of the person or organization communicating with you", "examples": "Examples: <PERSON><PERSON>, <PERSON><PERSON>, PT Maju <PERSON>, CV Suks<PERSON>", "label": "Contact Name", "placeholder": "Enter full contact name"}, "notes": {"description": "Internal notes, preferences, or important information about the contact", "examples": "Examples:\n• Prefers communication in the morning\n• VIP customer - priority support\n• Interested in premium products\n• Previous order: #12345 on 2024-01-15\n• Speaks Indonesian and English", "label": "Notes", "placeholder": "Add additional notes about this contact..."}, "phone": {"description": "Primary phone number for WhatsApp communication. Include country code for international numbers.", "examples": "Examples:\n• +62-812-3456-7890 (Indonesia)\n• ******-123-4567 (US)\n• +44-20-7946-0958 (UK)\n• +86-138-0013-8000 (China)", "label": "Phone Number", "placeholder": "Enter phone number with country code"}, "position": {"description": "Position or role of the contact in their organization", "examples": "Examples: Sales Manager, CEO, Marketing Director, Customer Service Representative", "label": "Position", "placeholder": "Enter position or job title"}, "tags": {"description": "Labels to help categorize and organize contacts for better management", "examples": "Examples: customer, prospect, vip, supplier, partner, lead, inactive, priority", "label": "Tags", "placeholder": "Add tags for organization (optional)"}, "updatedAt": {"label": "Last Updated"}}, "filters": {"has_email": "<PERSON>", "has_phone": "Has Phone", "has_tags": "Has Tags"}, "headers": {"created_by": "Created By", "created_date": "Created Date", "email": "Email", "name": "Name", "notes_count": "Notes Count", "phone": "Phone", "tags": "Tags", "updated_date": "Updated Date"}, "labels": {"contact": "Contact", "created_by": "Created By", "interactions": "Interactions", "tag": "Tag"}, "page_subtitle": "Manage and track customer contacts for CS operations", "page_title": "Customer Contacts", "routes": {"add": "/contacts/new", "bulk": "/contacts/bulk", "edit": "/contacts/{{id}}"}, "sections": {"basicInformation": {"title": "Basic Information", "description": "Enter main details such as name, phone, and email."}, "classification": {"title": "Classification", "description": "Categorize this contact using tags."}, "additionalInformation": {"title": "Additional Information", "description": "Add company, position, address, or birthday details."}, "notes": {"title": "Notes", "description": "Add notes or remarks about this contact."}, "systemInformation": {"title": "System Information", "description": "System information such as created and updated dates."}}, "notes": "notes", "edit_contact": "Edit Contact", "delete_contact": "Delete Contact", "created_by": "Created By", "created_date": "Created Date", "updated_date": "Updated Date", "tags": "Tags", "stats": {"total_contacts": "Total Contacts", "active_contacts": "Active Contacts", "contacts_with_email": "Contacts with Email", "contacts_with_tags": "Contacts with Tags", "added_recently": "added recently", "all_contacts_description": "Total of all contacts in the system", "percentage_of_total": "% of total", "contact_status": "Contact Status", "top_tags": "Top Tags", "created_by": "Created By", "all_time": "All Time"}}