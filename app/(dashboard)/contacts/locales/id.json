{"bulk": {"headers": {"delete": {"0": "<PERSON><PERSON>", "1": "Telepon", "2": "Email", "3": "<PERSON><PERSON><PERSON><PERSON>", "4": "Tanggal Dibuat"}, "import": {"0": "<PERSON><PERSON>", "1": "Telepon", "2": "Email", "3": "Tag"}}, "itemName": "kontak", "itemNamePlural": "kontak", "subtitle": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, dan hapus rekord kontak secara massal", "title": "Kontak"}, "buttons": {"cancel": "<PERSON><PERSON>", "create": "Buat Kontak", "update": "<PERSON><PERSON><PERSON>"}, "date_filter_options": {"all": "Sepanjang W<PERSON>", "last_month": "<PERSON><PERSON><PERSON>", "last_week": "<PERSON><PERSON>", "this_month": "<PERSON><PERSON><PERSON>", "this_week": "<PERSON><PERSON>", "today": "<PERSON>", "yesterday": "<PERSON><PERSON><PERSON>"}, "errors": {"bulkImportError": "Error impor massal", "createError": "<PERSON><PERSON>r membuat kontak", "createFailed": "<PERSON><PERSON> membuat kontak", "fetchForDeleteFailed": "Gagal mengambil kontak", "fetchForUpdateFailed": "Gagal mengambil kontak untuk pembaruan massal", "importFailed": "<PERSON><PERSON><PERSON> gagal", "updateFailed": "<PERSON><PERSON> kontak"}, "fields": {"address": {"description": "<PERSON><PERSON><PERSON> atau informasi lokasi untuk kontak", "examples": "Contoh:\n• Jl<PERSON> No. 45, Jakarta Pusat, Indonesia\n• Jl. <PERSON> 123, Yogyakarta 55271\n• Gedung ABC Lt. 5, <PERSON><PERSON><PERSON>, Jakarta\n• Komplek Perumahan Indah Blok A No. 10", "label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> alamat le<PERSON>p"}, "birthday": {"description": "Tanggal lahir untuk kontak personal (opsional)", "examples": "Contoh: 1990-05-15, 1985-12-25, 1992-03-08", "label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> tanggal lahir"}, "company": {"description": "Organisasi atau perusahaan tempat kontak bekerja", "examples": "Contoh: Microsoft Corporation, PT Telkom Indonesia, ABC Trading Ltd", "label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama <PERSON>an"}, "createdAt": {"label": "Tanggal Dibuat"}, "createdBy": {"label": "Dibuat Oleh"}, "email": {"description": "Alamat email utama untuk komunikasi dan identifikasi kontak", "examples": "Contoh: <EMAIL>, <EMAIL>, <EMAIL>", "label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> email"}, "name": {"description": "<PERSON>a lengkap orang atau organisasi yang berkomunikasi dengan <PERSON>a", "examples": "Contoh: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CV <PERSON>", "label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama lengkap kontak"}, "notes": {"description": "Catatan internal, preferensi, atau informasi penting tentang kontak", "examples": "Contoh:\n• Lebih suka komunikasi di pagi hari\n• Pelanggan VIP - dukungan prioritas\n• Tertarik dengan produk premium\n• Pesanan sebelumnya: #12345 pada 15-01-2024\n• Berbicara bahasa Indonesia dan Inggris", "label": "Catatan", "placeholder": "Tambahkan catatan tambahan tentang kontak ini..."}, "phone": {"description": "Nomor telepon utama untuk komunikasi WhatsApp. Sertakan kode negara untuk nomor internasional.", "examples": "Contoh:\n• +62-812-3456-7890 (format Indonesia)\n• ******-123-4567 (format AS)\n• +44-20-7946-0958 (format Inggris)\n• +86-138-0013-8000 (format China)", "label": "Nomor Telepon", "placeholder": "Ma<PERSON>kkan nomor telepon dengan kode negara"}, "position": {"description": "Jabatan atau peran kontak dalam organisasi mereka", "examples": "Contoh: <PERSON><PERSON><PERSON>, CEO, <PERSON><PERSON><PERSON>, Customer Service Representative", "label": "<PERSON><PERSON><PERSON>", "placeholder": "Masukkan jabatan atau posisi"}, "tags": {"description": "Label untuk membantu mengkategorikan dan mengorganisir kontak untuk manajemen yang lebih baik", "examples": "Contoh: pelanggan, prospek, vip, supplier, mitra, lead, tidak-aktif, prioritas", "label": "Tag", "placeholder": "Tambahkan tag untuk organisasi (opsional)"}, "updatedAt": {"label": "<PERSON><PERSON><PERSON>"}}, "filters": {"has_email": "Me<PERSON><PERSON><PERSON>", "has_phone": "Memiliki Telepon", "has_tags": "Memiliki Tag"}, "headers": {"created_by": "Dibuat Oleh", "created_date": "Tanggal Dibuat", "email": "Email", "name": "<PERSON><PERSON>", "notes_count": "<PERSON><PERSON><PERSON>", "phone": "Telepon", "tags": "Tag", "updated_date": "<PERSON><PERSON>"}, "labels": {"contact": "Kontak", "created_by": "Dibuat Oleh", "interactions": "Interaksi", "tag": "Tag"}, "page_subtitle": "Ke<PERSON>la dan pantau kontak pelanggan untuk operasi CS", "page_title": "Kontak Pelanggan", "routes": {"add": "/contacts/new", "bulk": "/contacts/bulk", "edit": "/contacts/{{id}}"}, "sections": {"basicInformation": {"title": "Informasi <PERSON>", "description": "Masukkan detail utama kontak seperti nama, telepon, dan email."}, "classification": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Kategorikan kontak ini menggunakan tag."}, "additionalInformation": {"title": "Informasi <PERSON>", "description": "Tambahkan detail per<PERSON><PERSON><PERSON>, jaba<PERSON>, alama<PERSON>, atau tanggal lahir."}, "notes": {"title": "Catatan", "description": "Tambahkan catatan atau keterangan tentang kontak ini."}, "systemInformation": {"title": "Informasi Sistem", "description": "Informasi sistem seperti tanggal dibuat dan diperbarui."}}, "notes": "catatan", "edit_contact": "<PERSON>", "delete_contact": "Hapus Kontak", "created_by": "Dibuat oleh", "created_date": "Tanggal Dibuat", "updated_date": "<PERSON><PERSON>", "tags": "Tag", "stats": {"total_contacts": "Total Kontak", "active_contacts": "Kontak Aktif", "contacts_with_email": "Kontak dengan Email", "contacts_with_tags": "Kontak dengan Tag", "added_recently": "ditambahkan baru-baru ini", "all_contacts_description": "Total semua kontak dalam sistem", "percentage_of_total": "% dari total", "contact_status": "Status Kontak", "top_tags": "Tag Teratas", "created_by": "Dibuat Oleh", "all_time": "Sepanjang W<PERSON>"}}