{"buttons": {"add_source": "Add Source", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "save_source": "Save Source", "update_source": "Update Source"}, "confirmations": {"delete": "Are you sure you want to delete this data source?"}, "detail_page": {"back_to_list": "Back to Data Sources List", "delete_confirmation": "Are you sure you want to delete this data source? This action cannot be undone.", "delete_error": "Failed to delete data source. Please try again.", "edit": {"description": "Update data source details", "submit": "Save Changes", "title": "Edit Data Source"}, "loading": "Loading data source...", "not_found": "Data source not found"}, "dialog": {"add_title": "Add New Data Source", "delete_description": "Are you sure you want to delete data source \"{{name}}\"?", "delete_title": "Delete Data Source", "edit_title": "Edit Data Source"}, "errors": {"delete_alert": "Failed to delete data source. Please try again.", "delete_failed": "Failed to delete data source", "fetch_failed": "Failed to fetch data sources"}, "form": {"labels": {"access_key": "Access Key (optional)", "name": "Name", "type": "Type", "url": "URL / Identifier"}, "placeholders": {"access_key": "Enter access key", "name": "Enter data source name", "search": "Search AI data sources...", "type": "Select type", "url": "https://sheet.google.com/..."}}, "new_datasource": "New Data Source", "new_page": {"description": "Add a new data source to the collection", "submit": "Create Data Source", "title": "Create New Data Source"}, "page_subtitle": "Manage data sources and content", "page_title": "Data Sources", "refresh": "Refresh", "search_placeholder": "Search data sources...", "states": {"create_first": "Create your first data source to get started", "loading": "Loading data sources...", "no_datasources": "No data sources found"}, "table": {"content": {"no_content": "No content", "no_url": "No URL"}, "description": "Manage data sources and content", "headers": {"actions": "Actions", "content_url": "Content/URL", "created": "Created", "name": "Name", "status": "Status", "type": "Type"}, "status": {"active": "Active", "inactive": "Inactive"}, "title": "Data Sources ({{count}})"}, "types": {"csv": "CSV", "firebase": "Firebase", "google_sheet": "GoogleSheet", "json": "JSON", "rest_api": "REST API", "text": "Text", "api": "API"}, "validation": {"name_required": "Name is required", "type_required": "Type is required", "url_required": "URL is required"}}