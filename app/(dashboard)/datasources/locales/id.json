{"buttons": {"add_source": "Tambah Sumber", "cancel": "<PERSON><PERSON>", "delete": "Hapus", "deleting": "Mengh<PERSON>us...", "save_source": "Simpan <PERSON>", "update_source": "<PERSON><PERSON><PERSON>"}, "confirmations": {"delete": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus sumber data ini?"}, "detail_page": {"back_to_list": "Ke<PERSON>li ke Daftar Sumber Data", "delete_confirmation": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus sumber data ini? Operasi ini tidak dapat dibatalkan.", "delete_error": "Gagal menghapus sumber data. Silakan coba lagi.", "edit": {"description": "Perbarui detail sumber data", "submit": "<PERSON><PERSON><PERSON>", "title": "Edit Sumber Data"}, "loading": "Memuat sumber data...", "not_found": "Sumber data tidak ditemukan"}, "dialog": {"add_title": "Tambah Sumber Data <PERSON>", "delete_description": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus sumber data \"{{name}}\"?", "delete_title": "Hapus Sumber Data", "edit_title": "Edit Sumber Data"}, "errors": {"delete_alert": "Gagal menghapus sumber data. Silakan coba lagi.", "delete_failed": "Gagal menghapus sumber data", "fetch_failed": "Gagal mengambil sumber data"}, "form": {"labels": {"access_key": "<PERSON><PERSON><PERSON> (opsional)", "name": "<PERSON><PERSON>", "type": "Tipe", "url": "URL / Pengenal"}, "placeholders": {"access_key": "<PERSON><PERSON><PERSON><PERSON> kunci akses", "name": "Masukkan nama sumber data", "search": "Cari sumber data AI...", "type": "<PERSON><PERSON><PERSON> tipe", "url": "https://sheet.google.com/..."}}, "new_datasource": "Sumber <PERSON>", "new_page": {"description": "Tambahkan sumber data baru ke koleksi", "submit": "Buat Sumber Data", "title": "Buat Sumber Data Baru"}, "page_subtitle": "Kelola sumber data dan konten", "page_title": "Sumber Data", "refresh": "Segarkan", "search_placeholder": "Cari sumber data...", "states": {"create_first": "Buat sumber data pertama untuk memulai", "loading": "Memuat sumber data...", "no_datasources": "Tidak ada sumber data ditemukan"}, "table": {"content": {"no_content": "Tidak ada konten", "no_url": "Tidak ada URL"}, "description": "Kelola sumber data dan konten", "headers": {"actions": "<PERSON><PERSON><PERSON>", "content_url": "Konten/URL", "created": "Dibuat", "name": "<PERSON><PERSON>", "status": "Status", "type": "Tipe"}, "status": {"active": "Aktif", "inactive": "Tidak Aktif"}, "title": "Sumber Data ({{count}})"}, "types": {"csv": "CSV", "firebase": "Firebase", "google_sheet": "GoogleSheet", "json": "JSON", "rest_api": "REST API", "text": "Teks", "api": "API"}, "validation": {"name_required": "<PERSON><PERSON> wa<PERSON><PERSON> di<PERSON>", "type_required": "<PERSON><PERSON><PERSON> wajib diisi", "url_required": "URL wajib diisi"}}