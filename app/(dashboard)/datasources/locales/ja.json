{"buttons": {"add_source": "ソースを追加", "cancel": "キャンセル", "delete": "削除", "deleting": "削除中...", "save_source": "ソースを保存", "update_source": "ソースを更新"}, "confirmations": {"delete": "このデータソースを削除してもよろしいですか？"}, "detail_page": {"back_to_list": "データソース一覧に戻る", "delete_confirmation": "このデータソースを削除してもよろしいですか？ この操作は取り消せません。", "delete_error": "データソースの削除に失敗しました。もう一度お試しください。", "edit": {"description": "データソースの詳細を更新してください", "submit": "変更を保存", "title": "データソースを編集"}, "loading": "データソースを読み込み中...", "not_found": "データソースが見つかりません"}, "dialog": {"add_title": "新しいデータソースを追加", "delete_description": "データソース「{{name}}」を削除してもよろしいですか？", "delete_title": "データソースを削除", "edit_title": "データソースを編集"}, "errors": {"delete_alert": "データソースの削除に失敗しました。もう一度お試しください。", "delete_failed": "データソースの削除に失敗しました", "fetch_failed": "データソースの取得に失敗しました"}, "form": {"labels": {"access_key": "アクセスキー（任意）", "name": "名前", "type": "タイプ", "url": "URL / 識別子"}, "placeholders": {"access_key": "アクセスキーを入力", "name": "データソース名を入力", "search": "AIデータソースを検索...", "type": "タイプを選択", "url": "https://sheet.google.com/..."}}, "new_datasource": "新しいデータソース", "new_page": {"description": "コレクションに新しいデータソースを追加します", "submit": "データソースを作成", "title": "新しいデータソースを作成"}, "page_subtitle": "データソースとコンテンツを管理します", "page_title": "データソース", "refresh": "更新", "search_placeholder": "データソースを検索...", "states": {"create_first": "最初のデータソースを作成して開始します", "loading": "データソースを読み込み中...", "no_datasources": "データソースが見つかりません"}, "table": {"content": {"no_content": "コンテンツなし", "no_url": "URLなし"}, "description": "データソースとコンテンツを管理します", "headers": {"actions": "操作", "content_url": "コンテンツ/URL", "created": "作成日", "name": "名前", "status": "ステータス", "type": "タイプ"}, "status": {"active": "有効", "inactive": "無効"}, "title": "データソース ({{count}})"}, "types": {"csv": "CSV", "firebase": "Firebase", "google_sheet": "GoogleSheet", "json": "JSON", "rest_api": "REST API", "text": "テキスト", "api": "API"}, "validation": {"name_required": "名前は必須です", "type_required": "タイプは必須です", "url_required": "URLは必須です"}}