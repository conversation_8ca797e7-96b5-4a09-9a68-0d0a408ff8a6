"use client"

import { useLocalization } from "@/localization/functions/client"
import { knowledgeBaseLocales } from "./locales"

import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default function KnowledgeBaseLandingPage() {
  const { t } = useLocalization("knowledgeBase", knowledgeBaseLocales)

  return (
    <div className="flex-1 overflow-auto">
      <div className="container mx-auto max-w-4xl p-6 space-y-8">
        {/* Header Section */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold">{t("title")}</h1>
          <p className="text-lg text-gray-700">{t("description")}</p>
        </div>

        {/* Why Use Section */}
        <section className="space-y-4 bg-gray-50 p-6 rounded-lg border border-gray-200">
          <h2 className="text-xl font-semibold">
            {t("landing.why_use_title")}
          </h2>
          <ul className="list-disc pl-5 space-y-2 text-gray-700">
            <li>{t("landing.why_use_item_1")}</li>
            <li>{t("landing.why_use_item_2")}</li>
            <li>{t("landing.why_use_item_3")}</li>
            <li>{t("landing.why_use_item_4")}</li>
            <li>{t("landing.why_use_item_5")}</li>
          </ul>
        </section>

        {/* AI Generation Section */}
        <section className="space-y-4 bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h2 className="text-xl font-semibold text-blue-800">
            🤖 {t("landing.ai_generation_title")}
          </h2>
          <p className="text-blue-700">{t("landing.ai_generation_desc")}</p>
          <ul className="list-disc pl-5 space-y-2 text-blue-700">
            <li>{t("landing.ai_generation_item_1")}</li>
            <li>{t("landing.ai_generation_item_2")}</li>
            <li>{t("landing.ai_generation_item_3")}</li>
          </ul>
          <div className="bg-blue-100 p-4 rounded-md border border-blue-300">
            <p className="text-sm text-blue-800 font-medium">
              💡 {t("landing.recommendation")}
            </p>
          </div>
        </section>

        {/* Get Started Section */}
        <section className="space-y-4 bg-green-50 p-6 rounded-lg border border-green-200">
          <h2 className="text-xl font-semibold text-green-800">
            {t("landing.get_started_title")}
          </h2>
          <p className="text-green-700">{t("landing.get_started_desc")}</p>
          <div className="flex gap-4 mt-4">
            <Link href="/knowledge-base/text">
              <Button className="bg-green-600 hover:bg-green-700">
                {t("start_editing")}
              </Button>
            </Link>
          </div>
        </section>
      </div>
    </div>
  )
}
