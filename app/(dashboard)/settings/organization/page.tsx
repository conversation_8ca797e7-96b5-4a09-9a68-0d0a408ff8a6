"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Building2, Save, Users, Link } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { OrganizationsAPI, OrganizationUpdatePayload } from "@/lib/services/organizationsApi"
import { CollaborationAPI, Collaborator } from "@/lib/services/collaborationApi"
import { useOrganization } from "@/lib/contexts/OrganizationContext"
import { MembersList } from "@/components/organization/MembersList"
import { InvitationLinkDialog } from "@/components/organization/InvitationLinkDialog"
import { MemberEditDialog } from "@/components/organization/MemberEditDialog"
import { locales } from "./locales"

export default function EditCurrentOrganizationPage() {
  const { t } = useLocalization("edit-organization", locales)
  const router = useRouter()
  const { currentOrganization, refreshOrganizations } = useOrganization()

  const [formData, setFormData] = useState<OrganizationUpdatePayload>({
    name: "",
    isActive: true,
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  // Members management state
  const [members, setMembers] = useState<Collaborator[]>([])
  const [membersLoading, setMembersLoading] = useState(false)
  const [showInvitationDialog, setShowInvitationDialog] = useState(false)
  const [editingMember, setEditingMember] = useState<Collaborator | null>(null)
  const [showMemberEditDialog, setShowMemberEditDialog] = useState(false)

  // Load current organization data
  useEffect(() => {
    if (currentOrganization) {
      setFormData({
        name: currentOrganization.name,
        isActive: currentOrganization.isActive,
      })
      loadMembers()
    }
  }, [currentOrganization])

  const loadMembers = async () => {
    try {
      setMembersLoading(true)
      const result = await CollaborationAPI.All().request()
      setMembers(result.items || [])
    } catch (err) {
      console.error("Failed to load members:", err)
    } finally {
      setMembersLoading(false)
    }
  }

  const handleEditMember = (member: Collaborator) => {
    setEditingMember(member)
    setShowMemberEditDialog(true)
  }

  const handleRemoveMember = async (member: Collaborator) => {
    if (confirm(t("confirm_remove_member"))) {
      try {
        await CollaborationAPI.RemoveCollaborator(member.id).request()
        await loadMembers()
      } catch (err) {
        console.error("Failed to remove member:", err)
      }
    }
  }

  const handleMemberUpdated = () => {
    loadMembers()
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!currentOrganization) {
      setError(t("no_organization"))
      return
    }

    if (!formData.name?.trim()) {
      setError(t("name_required"))
      return
    }

    try {
      setLoading(true)
      setError("")
      setSuccess("")

      await OrganizationsAPI.Update(currentOrganization.id, formData).request()

      // Refresh organizations in context
      await refreshOrganizations()

      setSuccess(t("update_success"))
    } catch (err: any) {
      console.error("Error updating organization:", err)
      if (err.message?.includes("duplicate") || err.message?.includes("already exists")) {
        setError(t("duplicate_name"))
      } else {
        setError(t("update_error"))
      }
    } finally {
      setLoading(false)
    }
  }

  if (!currentOrganization) {
    return (
      <div className="w-full py-6 space-y-6">
        <div className="text-center">
          <Building2 className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h1 className="text-2xl font-bold text-gray-600">{t("no_organization_title")}</h1>
          <p className="text-gray-500 mb-6">{t("no_organization_description")}</p>
          <Button onClick={() => router.push("/settings/organizations")}>
            {t("go_to_organizations")}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-2">
        <Building2 className="w-6 h-6" />
        <h1 className="text-2xl font-bold">{t("title")}</h1>
      </div>

      {/* Organization Details Form */}
      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle>{t("form_title")}</CardTitle>
          <CardDescription>{t("form_description")}</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                {error}
              </div>
            )}

            {success && (
              <div className="p-3 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md">
                {success}
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="name">{t("name_label")}</Label>
              <Input
                id="name"
                type="text"
                placeholder={t("name_placeholder")}
                value={formData.name || ""}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
              <p className="text-sm text-gray-500">{t("name_help")}</p>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, isActive: checked as boolean })
                }
              />
              <Label htmlFor="isActive" className="text-sm font-normal">
                {t("active_label")}
              </Label>
            </div>

            <div className="flex justify-end space-x-3">
              {/* <Button
                type="button"
                variant="outline"
                disabled={loading}
                onClick={() => router.push("/settings/organizations")}
              >
                {t("view_all_organizations")}
              </Button> */}
              <Button type="submit" disabled={loading}>
                <Save className="w-4 h-4 mr-2" />
                {loading ? t("saving") : t("save")}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Members Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Users className="w-6 h-6" />
            <div>
              <h2 className="text-xl font-bold">{t("members_section_title")}</h2>
              <p className="text-gray-600">{t("members_section_description")}</p>
            </div>
          </div>
          <Button onClick={() => setShowInvitationDialog(true)}>
            <Link className="w-4 h-4 mr-2" />
            {t("generate_invitation_link")}
          </Button>
        </div>

        <MembersList
          members={members}
          loading={membersLoading}
          onEditMember={handleEditMember}
          onRemoveMember={handleRemoveMember}
          t={t}
        />
      </div>

      {/* Dialogs */}
      <InvitationLinkDialog
        open={showInvitationDialog}
        onOpenChange={setShowInvitationDialog}
        t={t}
      />

      <MemberEditDialog
        member={editingMember}
        open={showMemberEditDialog}
        onOpenChange={setShowMemberEditDialog}
        onMemberUpdated={handleMemberUpdated}
        t={t}
      />
    </div>
  )
}
