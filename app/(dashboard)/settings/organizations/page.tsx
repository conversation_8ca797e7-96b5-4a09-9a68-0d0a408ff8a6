"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Plus, Edit, Trash2, Search } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { OrganizationsAPI, OrganizationCreatePayload, OrganizationUpdatePayload } from "@/lib/services/organizationsApi"
import { Organization } from "@/lib/repositories/organizations/interface"
import { locales } from "./locales"

export default function OrganizationsPage() {
  const { t } = useLocalization("organizations", locales)

  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [search, setSearch] = useState("")

  // Form state
  const [showForm, setShowForm] = useState(false)
  const [editingOrganization, setEditingOrganization] = useState<Organization | null>(null)
  const [formData, setFormData] = useState<OrganizationCreatePayload>({
    name: "",
    isActive: true,
  })
  const [formLoading, setFormLoading] = useState(false)

  // Delete state
  const [deleteOrganization, setDeleteOrganization] = useState<Organization | null>(null)
  const [deleteLoading, setDeleteLoading] = useState(false)

  useEffect(() => {
    loadOrganizations()
  }, [search])

  const loadOrganizations = async () => {
    try {
      setLoading(true)
      setError("")
      const result = await OrganizationsAPI.All({
        page: 1,
        search: search || undefined,
        limit: 50,
      }).request()
      setOrganizations(result.items || [])
    } catch (err: any) {
      console.error("Error loading organizations:", err)
      setError(t("load_error"))
    } finally {
      setLoading(false)
    }
  }

  const handleCreate = () => {
    setEditingOrganization(null)
    setFormData({ name: "", isActive: true })
    setShowForm(true)
  }

  const handleEdit = (organization: Organization) => {
    setEditingOrganization(organization)
    setFormData({
      name: organization.name,
      isActive: organization.isActive,
    })
    setShowForm(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormLoading(true)
    setError("")
    setSuccess("")

    try {
      if (editingOrganization) {
        await OrganizationsAPI.Update(editingOrganization.id, formData as OrganizationUpdatePayload).request()
        setSuccess(t("update_success"))
      } else {
        await OrganizationsAPI.Create(formData).request()
        setSuccess(t("create_success"))
      }

      setShowForm(false)
      loadOrganizations()
    } catch (err: any) {
      setError(editingOrganization ? t("update_error") : t("create_error"))
    } finally {
      setFormLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!deleteOrganization) return

    setDeleteLoading(true)
    setError("")
    setSuccess("")

    try {
      await OrganizationsAPI.Delete(deleteOrganization.id).request()
      setSuccess(t("delete_success"))
      setDeleteOrganization(null)
      loadOrganizations()
    } catch (err: any) {
      setError(t("delete_error"))
    } finally {
      setDeleteLoading(false)
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(new Date(date))
  }

  return (
    <div className="w-full py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">{t("title")}</h1>
          <p className="text-gray-600">{t("description")}</p>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="w-4 h-4 mr-2" />
          {t("create_button")}
        </Button>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder={t("search_placeholder")}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Messages */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Organizations Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t("title")}</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">{t("loading")}</div>
          ) : organizations.length === 0 ? (
            <div className="text-center py-8 text-gray-500">{t("no_organizations")}</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("name")}</TableHead>
                  <TableHead>{t("status")}</TableHead>
                  <TableHead>{t("created_at")}</TableHead>
                  <TableHead className="text-right">{t("actions")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {organizations.map((organization) => (
                  <TableRow key={organization.id}>
                    <TableCell className="font-medium">{organization.name}</TableCell>
                    <TableCell>
                      <Badge variant={organization.isActive ? "default" : "secondary"}>
                        {organization.isActive ? t("active") : t("inactive")}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatDate(organization.createdAt)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(organization)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setDeleteOrganization(organization)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingOrganization ? t("edit_title") : t("create_title")}
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t("name_label")}</Label>
              <Input
                id="name"
                placeholder={t("name_placeholder")}
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
                disabled={formLoading}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, isActive: checked as boolean })
                }
                disabled={formLoading}
              />
              <Label htmlFor="isActive">{t("is_active_label")}</Label>
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowForm(false)}
                disabled={formLoading}
              >
                {t("cancel_button")}
              </Button>
              <Button type="submit" disabled={formLoading}>
                {formLoading
                  ? (editingOrganization ? t("updating") : t("creating"))
                  : t("save_button")
                }
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteOrganization} onOpenChange={() => setDeleteOrganization(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("delete_title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("delete_message")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteLoading}>
              {t("cancel_button")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={deleteLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteLoading ? t("deleting") : t("delete_confirm")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
