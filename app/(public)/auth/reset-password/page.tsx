import ResetPasswordPageClient from "./client"
import { localize } from "@/localization/functions/server"
import { locales as commonLocales } from "@/components/locales"

export default async function ResetPasswordPageServer({
  searchParams,
}: {
  searchParams: Promise<{ token?: string }>
}) {
  const { t } = await localize("common", commonLocales as any)

  const token = (await searchParams).token
  if (!token) {
    return <div>{t("errors.invalid_token")}</div>
  }

  return <ResetPasswordPageClient params={{ token }} />
}
