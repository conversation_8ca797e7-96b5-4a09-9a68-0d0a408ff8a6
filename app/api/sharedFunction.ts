import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { NextRequest, NextResponse } from "next/server"
import { ERROR_CODES } from "./error_codes"
import { SessionContext } from "@/lib/repositories/auth/types"
import { cookies } from "next/headers"

type SessionContextSuccess = { context: SessionContext; response: null }
type SessionContextApiError = { response: NextResponse; context: null }

export async function buildSessionContext(
  nextRequest: NextRequest,
): Promise<SessionContextSuccess | SessionContextApiError> {
  const { authBusinessLogic } = getBusinessLogics()
  const token = nextRequest.headers.get("x-user-access-token")!
  const organizationId = nextRequest.headers.get("x-organization-id")!

  let validateResult = await authBusinessLogic.validateToken(token)

  if (!validateResult.user) {
    return {
      context: null,
      response: NextResponse.json(
        ResponseBuilder.fail(
          validateResult.error ? [validateResult.error] : [],
          [ERROR_CODES.INVALID_TOKEN],
        ),
        { status: 401 },
      ),
    }
  }

  return {
    response: null,
    context: {
      user: {
        id: validateResult.user.id,
        email: validateResult.user.email,
        name: validateResult.user.name || "",
      },
      organization: organizationId ? { id: organizationId } : undefined,
    },
  }
}

export async function buildSessionContextFromCookie(): Promise<SessionContext | null> {
  const { authBusinessLogic } = getBusinessLogics()
  const cookieStore = await cookies()
  const token = cookieStore.get("token")?.value
  const refreshToken = cookieStore.get("refresh_token")?.value
  const organizationId = cookieStore.get("organization_id")?.value

  let validateResult: {
    user?: { id: string; email: string; name: string }
    error?: "expired" | "token-not-found" | string
  } = {
    error: "token-not-found",
  }
  if (token) {
    const tokenResult = await authBusinessLogic.validateToken(token)
    validateResult = {
      user: tokenResult.user && {
        id: tokenResult.user.id,
        email: tokenResult.user.email,
        name: tokenResult.user.name || "",
      },
      error: tokenResult.error,
    }
  }

  if (!validateResult.user && refreshToken) {
    const user = await authBusinessLogic.validateUserWithRefreshToken({
      refresh_token: refreshToken,
    })
    if (user) {
      validateResult = {
        user: {
          id: user.id,
          email: user.email,
          name: user.name || "",
        },
      }
    }
  }

  if (!validateResult.user) return null

  return {
    user: {
      id: validateResult.user.id,
      email: validateResult.user.email,
      name: validateResult.user.name || "",
    },
    organization: organizationId ? { id: organizationId } : undefined,
  }
}
