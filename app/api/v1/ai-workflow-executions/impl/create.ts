import { realtime } from "@/lib/realtime"
import { RealtimeWorkflow } from "@/lib/realtime/model"
import {
  AiWorkflowExecutionBusinessLogicInterface,
  AiWorkflowExecutionCreateInput,
} from "@/lib/repositories/aiWorkflowExecutions"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ResponseBuilder, ResponseWrapper } from "@/lib/types/responseWrapper"
import {
  AiWorkflowExecutionResponses,
  createValidationErrorResponse,
} from "@/lib/utils/api-response-helper"
import { AiWorkflowExecutionCreateSchema } from "@/lib/validations/workflowExecution"

// Create AiWorkflowExecution Implementation
export async function implHandleCreateAiWorkflowExecution(
  data: any,
  businessLogic: AiWorkflowExecutionBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate input data
    const validationResult = AiWorkflowExecutionCreateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )
      return createValidationErrorResponse(errors)
    }

    const validData = validationResult.data
    const createData: AiWorkflowExecutionCreateInput = {
      ...validData,
      customerName: "",
      startTime: new Date(),
      totalDuration: 0,
      finalStatus: "resolved",
      steps: [],
    }

    const created = await businessLogic.create(createData, context)

    await RealtimeWorkflow.NEW_WORKFLOW(created).send()

    return AiWorkflowExecutionResponses.created(created)
  } catch (error: any) {
    console.error("Create AiWorkflowExecution error:", error)

    // Handle known error codes if relevant to your schema
    if (error.code === "DUPLICATE_ID") {
      return AiWorkflowExecutionResponses.duplicateId()
    }

    return AiWorkflowExecutionResponses.createFailed()
  }
}
