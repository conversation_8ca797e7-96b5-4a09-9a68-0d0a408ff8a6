import {
  AiWorkflowExecutionBusinessLogicInterface,
  AiWorkflowExecution,
} from "@/lib/repositories/aiWorkflowExecutions/interface"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ResponseBuilder, ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { GetAllResultPaginated, AiWorkflowExecutionQueryParams } from "./types"
import {
  AiWorkflowExecutionResponses,
  SearchResponses,
  createSuccessResponse,
} from "@/lib/utils/api-response-helper"

// Get All AiWorkflowExecutions Implementation (handles all, search, and tag filtering)
export async function implHandleGetAllAiWorkflowExecutions(
  businessLogic: AiWorkflowExecutionBusinessLogicInterface,
  params: AiWorkflowExecutionQueryParams,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<GetAllResultPaginated<AiWorkflowExecution>>
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === "") {
        return SearchResponses.emptyKeyword()
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === "") {
          return SearchResponses.emptyFilterField()
        }
      }
    }

    // Validate sort if provided
    if (params?.sort && params.sort.length > 0) {
      for (const sort of params.sort) {
        if (!sort.field || sort.field.trim() === "") {
          return SearchResponses.emptySortField()
        }
        if (!["ASC", "DESC"].includes(sort.direction)) {
          return SearchResponses.invalidSortDirection()
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    }

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim()
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters
    }

    // Add sort if provided
    if (params?.sort && params.sort.length > 0) {
      queryParams.sort = params.sort
    }

    const result = await businessLogic.getAll(queryParams, context)

    return createSuccessResponse<GetAllResultPaginated<AiWorkflowExecution>>({
      ...result,
      page: queryParams.page,
    })
  } catch (error: any) {
    console.error("Get workflowExecutions error:", error)

    return AiWorkflowExecutionResponses.fetchFailed()
  }
}
