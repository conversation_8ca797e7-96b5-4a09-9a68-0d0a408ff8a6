import { AiWorkflowExecutionBusinessLogicInterface } from "@/lib/repositories/aiWorkflowExecutions"
import { ResponseBuilder, ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import {
  AiWorkflowExecutionsStatsData,
  AiWorkflowExecutionStatsParams,
} from "./types"

// Get AiWorkflowExecutions Stats Implementation
export async function implHandleGetAiWorkflowExecutionsStats(
  businessLogic: AiWorkflowExecutionBusinessLogicInterface,
  params: AiWorkflowExecutionStatsParams,
): Promise<{
  status: number
  body: ResponseWrapper<AiWorkflowExecutionsStatsData>
}> {
  try {
    return {
      status: 200,
      body: ResponseBuilder.success({
        totalAiWorkflowExecutions: 0,
        activeAiWorkflowExecutions: 0,
        deletedAiWorkflowExecutions: 0,
        workflowExecutionsWithEmail: 0,
        workflowExecutionsWithTags: 0,
        recentAiWorkflowExecutions: 0,
        statusBreakdown: [],
        tagBreakdown: [],
        createdByBreakdown: [],
        dailyStats: [],
      }),
    }
  } catch (error: any) {
    console.error("Get workflowExecutions stats error:", error)

    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Failed to fetch workflowExecutions statistics. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}
