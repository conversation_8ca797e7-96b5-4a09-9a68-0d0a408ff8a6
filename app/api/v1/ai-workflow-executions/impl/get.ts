import { AiWorkflowExecutionBusinessLogicInterface } from "@/lib/repositories/aiWorkflowExecutions"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ResponseBuilder, ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"

// Get AiWorkflowExecution by ID Implementation
export async function implHandleGetAiWorkflowExecution(
  id: string,
  businessLogic: AiWorkflowExecutionBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const workflowExecutions = await businessLogic.getById(id, context)

    if (!workflowExecutions) {
      return {
        status: 404,
        body: ResponseBuilder.fail(
          ["AiWorkflowExecution not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: ResponseBuilder.success(workflowExecutions),
    }
  } catch (error: any) {
    console.error("Get workflowExecutions error:", error)

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Failed to fetch workflowExecutions. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}
