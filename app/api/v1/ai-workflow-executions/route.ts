import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import {
  implHandleGetAllAiWorkflowExecutions,
  implHandleCreateAiWorkflowExecution,
} from "./impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { workflowExecutionsSearchConfig } from "../search-configs/entities/workflowExecutions"
import { buildSessionContext } from "@/app/api/sharedFunction"

export async function GET(req: NextRequest) {
  const { workflowExecutionsBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { searchParams } = new URL(req.url)

    const search = searchParams.get("search") || undefined
    const includeDeleted = searchParams.get("includeDeleted") === "true"
    const page = searchParams.get("page")
      ? parseInt(searchParams.get("page")!)
      : undefined
    const limit = searchParams.get("limit")
      ? parseInt(searchParams.get("limit")!)
      : undefined

    const { result: searchConfigResult, errors: searchConfigErrors } =
      workflowExecutionsSearchConfig.parseParams(searchParams)

    if (searchConfigErrors) {
      return NextResponse.json(
        ResponseBuilder.fail(searchConfigErrors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
        { status: 400 },
      )
    }

    const sort = searchConfigResult?.sort || []
    const filters = searchConfigResult?.filters || []

    const params = {
      search,
      includeDeleted,
      page,
      limit,
      sort,
      filters,
    }

    const result = await implHandleGetAllAiWorkflowExecutions(
      workflowExecutionsBusinessLogic,
      params,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("AiWorkflowExecutions GET route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function POST(req: NextRequest) {
  const { workflowExecutionsBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const result = await implHandleCreateAiWorkflowExecution(
      body,
      workflowExecutionsBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("AiWorkflowExecutions POST route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
