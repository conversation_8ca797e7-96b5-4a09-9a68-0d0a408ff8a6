import { SessionContext } from "@/lib/repositories/auth/types"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { implHandleSendMessage } from "../../functions/messages/send/impl"
import { getExecutionIdAiExtras } from "../../functions/webhook/impl"
import { AiBusinessLogic } from "./AIBusinessLogic"

export async function implHandleTryAnswer(
  conversationId: string,
  executionId: string,
  body: { message: string },
  businessLogic: AiBusinessLogic,
  context: SessionContext,
) {
  const rules = await businessLogic.tryAnswer(
    conversationId,
    executionId,
    body.message,
    context,
  )
  return {
    status: 200,
    body: ResponseBuilder.success(rules, ["Executed TRY_ANSWER"])
  }
}

export async function implHandleReply(
  sessionId: string,
  executionId: string,
  body: { final_message: string },
  businessLogic: AiBusinessLogic,
  context: SessionContext,
) {
  try {
    const cs_ai_extras = await getExecutionIdAiExtras(executionId)
    if (!cs_ai_extras) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          ["Conversation context not found"],
          ["CONVERSATION_CONTEXT_NOT_FOUND"],
        )
      }
    }

    await implHandleSendMessage(
      { conversationId: cs_ai_extras.msg_room_id, text: body.final_message },
      context,
      true,
    )
    return {
      status: 200,
      body: ResponseBuilder.success({}, ["Executed REPLY"]),
    }
  } catch (error) {
    console.error("❌ Error during AI workflow handling:", error)
    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Internal server error"],
        ["INTERNAL_SERVER_ERROR"],
      )
    }
  }
}
