import { ERROR_CODES } from "@/app/api/error_codes"
import { Response<PERSON>uilder } from "@/lib/types/responseWrapper"
import { NextRequest, NextResponse } from "next/server"
import { getExecutionIdContext } from "../../functions/webhook/impl"
import { implHandleReply } from "./impl"
import { getAiServices } from "./shared"

export async function POST(req: NextRequest) {
  const { aiBusinessLogic } = getAiServices()
  try {
    const body = await req.json()

    const context = await getExecutionIdContext(body.execution_id)
    if (!context) {
      return NextResponse.json(
        ResponseBuilder.fail(
          ["Workflow execution not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
        { status: 404 },
      )
    }

    if (!body.context) {
      return NextResponse.json(
        ResponseBuilder.fail(
          ["context field is required"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
        { status: 400 },
      )
    }

    let result
    const ctx = body.context.toUpperCase()
    const sessionId = body.session_id
    const executionId = body.execution_id

    switch (ctx) {
      case "REPLY":
        result = await implHandleReply(
          sessionId,
          executionId,
          body,
          aiBusinessLogic,
          context,
        )
        break
      default:
        return NextResponse.json(
          ResponseBuilder.fail(
            [`Unsupported context: ${body.context}`],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
          { status: 400 },
        )
    }

    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("AI POST route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
