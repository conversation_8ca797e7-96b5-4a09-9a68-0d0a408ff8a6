import { Response<PERSON><PERSON>er, ResponseWrapper } from "@/lib/types/responseWrapper"
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth"
import { ChangePasswordSchema } from "@/lib/schemas/auth"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function implHandleChangePassword(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface,
  userId: string,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const validation = ChangePasswordSchema.safeParse(body)
    if (!validation.success) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          validation.error.errors.map(
            (err) => `${err.path.join(".")}: ${err.message}`,
          ),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const { currentPassword, newPassword } = validation.data

    const result = await authBusinessLogic.changePassword(
      userId,
      currentPassword,
      newPassword,
    )

    if (!result.success) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          [result.message],
          [ERROR_CODES.UPDATE_FAILED],
        ),
      }
    }

    return {
      status: 200,
      body: ResponseBuilder.success({ message: result.message }),
    }
  } catch (error) {
    console.error("Change password error:", error)
    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}
