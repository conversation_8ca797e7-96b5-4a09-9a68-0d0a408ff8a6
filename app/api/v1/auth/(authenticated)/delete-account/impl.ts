import { ERROR_CODES } from "@/app/api/error_codes"
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { DeleteAccountSchema } from "@/lib/schemas/auth"
import { ResponseBuilder, ResponseWrapper } from "@/lib/types/responseWrapper"

export async function implHandleDeleteAccount(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface,
  userId: string,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const validation = DeleteAccountSchema.safeParse(body)
    if (!validation.success) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          validation.error.errors.map(
            (err) => `${err.path.join(".")}: ${err.message}`,
          ),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const { password } = validation.data

    // Delete account
    const result = await authBusinessLogic.deleteAccount(userId, password)

    if (!result.success) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          [result.message],
          [ERROR_CODES.UNKNOWN_ERROR],
        ),
      }
    }

    return {
      status: 200,
      body: ResponseBuilder.success({ message: result.message }),
    }
  } catch (error) {
    console.error("Delete account error:", error)
    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}
