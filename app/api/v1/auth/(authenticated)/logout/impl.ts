import { ERROR_CODES } from "@/app/api/error_codes"
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { LogoutSchema } from "@/lib/schemas/auth"
import { ResponseBuilder, ResponseWrapper } from "@/lib/types/responseWrapper"

export async function implHandleLogout(
  token: string,
  authBusinessLogic: AuthBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    await authBusinessLogic.logout(token)

    return {
      status: 200,
      body: ResponseBuilder.success({
        message: "Logged out successfully",
      }),
    }
  } catch (error) {
    console.error("Logout error:", error)
    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}
