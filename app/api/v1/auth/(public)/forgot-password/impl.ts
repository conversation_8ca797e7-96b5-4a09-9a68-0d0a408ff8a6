import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { ForgotPasswordSchema } from "@/lib/schemas/auth"
import { ResponseBuilder, ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function implHandleForgotPassword(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate request body
    const validation = ForgotPasswordSchema.safeParse(body)
    if (!validation.success) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          validation.error.errors.map(
            (err) => `${err.path.join(".")}: ${err.message}`,
          ),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const { email } = validation.data

    // Request password reset
    const result = await authBusinessLogic.requestPasswordReset(email)

    return {
      status: 200,
      body: ResponseBuilder.success({
        message: result.message,
        resetToken: result.data?.resetToken, // Only for testing
      }),
    }
  } catch (error) {
    console.error("Forgot password error:", error)
    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}
