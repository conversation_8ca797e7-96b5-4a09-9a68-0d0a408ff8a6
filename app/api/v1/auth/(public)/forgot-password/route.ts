import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { implHandleForgotPassword } from "./impl"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function POST(req: NextRequest) {
  const { authBusinessLogic } = getBusinessLogics()
  try {
    const body = await req.json()
    const result = await implHandleForgotPassword(body, authBusinessLogic)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Forgot password route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(["Internal server error"], [ERROR_CODES.INTERNAL_SERVER_ERROR]),
      { status: 500 },
    )
  }
}
