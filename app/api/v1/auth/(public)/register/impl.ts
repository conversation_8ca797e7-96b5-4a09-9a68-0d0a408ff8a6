import { AuthBusinessLogicInterface } from "@/lib/repositories/auth"
import { Response<PERSON><PERSON><PERSON>, ResponseWrapper } from "@/lib/types/responseWrapper"
import { RegisterSchema } from "@/lib/schemas/auth"
import { ERROR_CODES } from "@/app/api/error_codes"
import { roleStore } from "@/lib/repositories/accessManagers"
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager"
import { localize } from "@/localization/functions/server"
import { locales } from "./locales"

export async function implHandleRegister(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface,
  accessManagers: {},
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  const { t } = await localize("register", locales)
  try {
    // Validate request body
    const validationResult = RegisterSchema.safeParse(body)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message)
      return {
        status: 400,
        body: ResponseBuilder.fail(errors, [ERROR_CODES.VALIDATION_FAILED]),
      }
    }

    const { email, password, name } = validationResult.data

    // Attempt registration
    const result = await authBusinessLogic.register({ email, password, name })

    await roleStore.setUserRoles(result.user.id, result.user.id, [
      { name: "admin", level: 10 },
    ])

    return {
      status: 201,
      body: ResponseBuilder.success(result),
    }
  } catch (error: any) {
    console.error("Registration error:", error)

    if (error.code === "USER_EXISTS") {
      return {
        status: 409,
        body: ResponseBuilder.fail(
          [t("error_user_exists")],
          [ERROR_CODES.USER_EXISTS],
        ),
      }
    }

    return {
      status: 500,
      body: ResponseBuilder.fail(
        [t("error_registration_failed")],
        [ERROR_CODES.REGISTRATION_FAILED],
      ),
    }
  }
}
