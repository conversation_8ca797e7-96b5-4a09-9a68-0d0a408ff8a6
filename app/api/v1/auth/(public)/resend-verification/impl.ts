import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { ResendVerificationSchema } from "@/lib/schemas/auth"
import { ResponseBuilder, ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function implHandleResendVerification(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate request body
    const validation = ResendVerificationSchema.safeParse(body)
    if (!validation.success) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          validation.error.errors.map(
            (err) => `${err.path.join(".")}: ${err.message}`,
          ),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const { email } = validation.data

    // Resend verification email
    const result = await authBusinessLogic.resendEmailVerification(email)

    if (!result.success) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          [result.message],
          [ERROR_CODES.INTERNAL_SERVER_ERROR],
        ),
      }
    }

    return {
      status: 200,
      body: ResponseBuilder.success({
        message: result.message,
        verificationToken: result.data?.verificationToken, // Only for testing
      }),
    }
  } catch (error) {
    console.error("Resend verification error:", error)
    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}
