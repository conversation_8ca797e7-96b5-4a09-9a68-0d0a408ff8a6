import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { ResetPasswordInput, ResetPasswordSchema } from "@/lib/schemas/auth"
import { ResponseBuilder, ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function implHandleResetPassword(
  body: ResetPasswordInput | any,
  authBusinessLogic: AuthBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate request body
    const validation = ResetPasswordSchema.safeParse(body)
    if (!validation.success) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          validation.error.errors.map(
            (err) => `${err.path.join(".")}: ${err.message}`,
          ),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const { token, newPassword } = validation.data

    // Reset password
    const result = await authBusinessLogic.resetPassword(token, newPassword)

    if (!result.success) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          [result.message],
          [ERROR_CODES.INVALID_TOKEN],
        ),
      }
    }

    return {
      status: 200,
      body: ResponseBuilder.success({ message: result.message }),
    }
  } catch (error) {
    console.error("Reset password error:", error)
    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}
