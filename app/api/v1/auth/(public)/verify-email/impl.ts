import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { VerifyEmailSchema } from "@/lib/schemas/auth"
import { ResponseBuilder, ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function implHandleVerifyEmail(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate request body
    const validation = VerifyEmailSchema.safeParse(body)
    if (!validation.success) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          validation.error.errors.map(
            (err) => `${err.path.join(".")}: ${err.message}`,
          ),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const { token } = validation.data

    // Verify email
    const result = await authBusinessLogic.verifyEmail(token)

    if (!result.success) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          [result.message],
          [ERROR_CODES.INVALID_TOKEN],
        ),
      }
    }

    return {
      status: 200,
      body: ResponseBuilder.success({ message: result.message }),
    }
  } catch (error) {
    console.error("Verify email error:", error)
    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}
