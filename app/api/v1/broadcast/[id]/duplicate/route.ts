import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { z } from "zod"
import { buildSessionContext } from "@/app/api/sharedFunction"

// Validation schema for duplicate broadcast request
const DuplicateBroadcastSchema = z.object({
  newTitle: z.string().optional(),
})

// POST /api/v1/broadcast/[id]/duplicate - Duplicate a broadcast
export async function POST(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { broadcastBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const body = await req.json()

    const validationResult = DuplicateBroadcastSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        ResponseBuilder.fail(
          validationResult.error.errors.map((e) => e.message),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
        { status: 400 },
      )
    }

    const { newTitle } = validationResult.data

    // Duplicate the broadcast
    const duplicatedBroadcast = await broadcastBusinessLogic.duplicate(
      id,
      context,
      newTitle,
    )

    return NextResponse.json(
      ResponseBuilder.success(duplicatedBroadcast, [
        "Broadcast duplicated successfully",
      ]),
      { status: 201 },
    )
  } catch (error: any) {
    console.error("Broadcast duplicate route error:", error)

    // Handle specific business logic errors
    if (error.code === "BROADCAST_NOT_FOUND") {
      return NextResponse.json(
        ResponseBuilder.fail(
          ["Broadcast not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
        { status: 404 },
      )
    }

    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
