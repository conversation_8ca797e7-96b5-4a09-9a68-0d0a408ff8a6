import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import {
  implHandleGetBroadcastById,
  implHandleUpdateBroadcast,
  implHandleDeleteBroadcast,
} from "../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { ResponseBuilder } from "@/lib/types/responseWrapper"

// GET /api/v1/broadcast/[id] - Get broadcast by ID
export async function GET(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { broadcastBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const { searchParams } = new URL(req.url)
    const includeDeleted = searchParams.get("includeDeleted") === "true"

    const result = await implHandleGetBroadcastById(
      id,
      broadcastBusinessLogic,
      context,
      includeDeleted,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in GET /api/v1/broadcast/[id]:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        [error.message || "Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

// PUT /api/v1/broadcast/[id] - Update broadcast
export async function PUT(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { broadcastBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const body = await req.json()

    const result = await implHandleUpdateBroadcast(
      id,
      body,
      broadcastBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in PUT /api/v1/broadcast/[id]:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        [error.message || "Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

// DELETE /api/v1/broadcast/[id] - Delete broadcast
export async function DELETE(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { broadcastBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const { searchParams } = new URL(req.url)
    const hardDelete = searchParams.get("hardDelete") === "true"

    const result = await implHandleDeleteBroadcast(
      id,
      broadcastBusinessLogic,
      context,
      hardDelete,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in DELETE /api/v1/broadcast/[id]:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        [error.message || "Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
