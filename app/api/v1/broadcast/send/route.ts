import { NextRequest, NextResponse } from "next/server"
import { BroadcastSendMessage } from "@/lib/queue/MessageQueue"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { implHandleBroadcastSend } from "./impl"

export async function POST(req: NextRequest) {
  try {
    const body: BroadcastSendMessage = await req.json()
    const result = await implHandleBroadcastSend(body)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error processing broadcast sender request:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        ["INTERNAL_SERVER_ERROR"],
      ),
      { status: 500 },
    )
  }
}
