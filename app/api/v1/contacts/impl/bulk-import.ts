import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface"
import { Response<PERSON><PERSON><PERSON>, ResponseWrapper } from "@/lib/types/responseWrapper"
import { ContactCreateSchema } from "@/lib/validations/contact"
import { ERROR_CODES } from "@/app/api/error_codes"
import { BulkImportRequest, BulkOperationResult } from "./types"
import {
  BulkResponses,
  ContactResponses,
  createSuccessResponse,
} from "@/lib/utils/api-response-helper"
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ContactAccessResource } from "./access_manager_resource"

// Bulk import implementation
export async function implBulkImportContacts(
  request: BulkImportRequest,
  businessLogic: ContactBusinessLogicInterface,
  context: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<ContactAccessResource>,
): Promise<{
  status: number
  body: ResponseWrapper<BulkOperationResult>
}> {
  // const isAuthorized = await accessManager?.isAllowed({
  //   resource: "POST /bulk",
  //   action: "create",
  //   userId: context?.user.id ?? "",
  // })

  // if (!isAuthorized) {
  //   return ContactResponses.unauthorizedRole()
  // }

  try {
    const { data } = request

    if (!Array.isArray(data)) {
      return BulkResponses.invalidDataFormat()
    }

    if (data.length === 0) {
      return BulkResponses.noDataProvided()
    }

    // Validate all data first
    const validatedData: any[] = []
    const validationErrors: Array<{
      row: number
      field: string
      message: string
    }> = []

    for (let i = 0; i < data.length; i++) {
      const contactData = data[i]

      // Validate using schema
      const validationResult = ContactCreateSchema.safeParse(contactData)
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(
          (err) => `${err.path.join(".")}: ${err.message}`,
        )
        validationErrors.push({
          row: i,
          field: "validation",
          message: errors.join(", "),
        })
      } else {
        // Transform notes to include createdAt
        const createData = {
          ...validationResult.data,
          notes: validationResult.data.notes?.map((note) => ({
            text: note.text,
            createdAt: new Date().toISOString(),
          })),
        }
        validatedData.push(createData)
      }
    }

    const results: BulkOperationResult = {
      total: data.length,
      successful: 0,
      failed: validationErrors.length,
      errors: validationErrors,
    }

    // If we have valid data, perform bulk create
    if (validatedData.length > 0) {
      try {
        const createdContacts = await businessLogic.bulkCreate(
          validatedData,
          context,
        )
        results.successful = createdContacts.length
      } catch (error: any) {
        // If bulk operation fails, mark all remaining as failed
        results.failed = data.length
        results.successful = 0
        results.errors = [
          {
            row: 0,
            field: "general",
            message:
              error instanceof Error
                ? error.message
                : "Bulk create operation failed",
          },
        ]
      }
    }

    return createSuccessResponse(results)
  } catch (error: any) {
    console.error("Bulk import contacts error:", error)

    return BulkResponses.importFailed()
  }
}
