import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface"
import { Response<PERSON><PERSON>er, ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ContactResponses } from "@/lib/utils/api-response-helper"
import { ContactAccessResource } from "./access_manager_resource"

// Get Contact by ID Implementation
export async function implHandleGetContact(
  id: string,
  businessLogic: ContactBusinessLogicInterface,
  context: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<ContactAccessResource>,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  // const isAuthorized = await accessManager?.isAllowed({
  //   resource: "GET /contacts",
  //   action: "read",
  //   userId: context?.user.id ?? "",
  // })

  // if (!isAuthorized) {
  //   return ContactResponses.unauthorizedRole()
  // }

  try {
    const contacts = await businessLogic.getById(id, context)

    if (!contacts) {
      return {
        status: 404,
        body: ResponseBuilder.fail(
          ["Contact not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: ResponseBuilder.success(contacts),
    }
  } catch (error: any) {
    console.error("Get contacts error:", error)

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Failed to fetch contacts. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}
