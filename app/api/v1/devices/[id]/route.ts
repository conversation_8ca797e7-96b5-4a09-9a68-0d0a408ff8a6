import { NextRequest, NextResponse } from "next/server"
import { implHandleUpdateDevice, implHandleDeleteDevice } from "../impl"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  const { devicesBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }
    const { id } = await params
    const device = await devicesBusinessLogic.getById(id, context)

    if (!device) {
      return NextResponse.json(
        ResponseBuilder.fail(
          ["Device not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
        { status: 404 },
      )
    }

    return NextResponse.json(
      ResponseBuilder.success(device),
      { status: 200 },
    )
  } catch (error) {
    console.error("Get device route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  const { devicesBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }
    const body = await req.json()
    const { id } = await params
    const result = await implHandleUpdateDevice(id, body, context)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Update device route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  const { devicesBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }
    const { id } = await params
    const result = await implHandleDeleteDevice(id, context)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Delete device route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
