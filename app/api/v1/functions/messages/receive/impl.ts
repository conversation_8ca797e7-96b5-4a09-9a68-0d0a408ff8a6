import { Response<PERSON><PERSON><PERSON>, ResponseWrapper } from "@/lib/types/responseWrapper"
import { ReceiveMessagesSchema } from "@/lib/schemas/messages"
import { providers } from "@/lib/providers"
import { getAuthCookie, setAuthCookie } from "@/lib/cookies"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function implHandleReceiveMessages(
  searchParams: URLSearchParams,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    let providerKey = searchParams.get("provider")
    const conversationId = searchParams.get("conversationId")
    const limit = searchParams.get("limit") || "20"
    const offset = searchParams.get("offset") || "0"

    // Validate query parameters
    const validationResult = ReceiveMessagesSchema.safeParse({
      provider: providerKey,
      conversationId,
      limit,
      offset,
    })

    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message)
      return {
        status: 400,
        body: ResponseBuilder.fail(errors, [ERROR_CODES.VALIDATION_FAILED]),
      }
    }

    const validatedData = validationResult.data

    if (!providerKey) {
      providerKey = (await getAuthCookie("preferred_provider")) || "waha"
    }

    const provider = providers[providerKey]

    if (!provider) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          [`Provider "${providerKey}" is not available.`],
          [ERROR_CODES.PROVIDER_NOT_FOUND],
        ),
      }
    }

    if (!getAuthCookie("preferred_provider") && providerKey) {
      setAuthCookie("preferred_provider", providerKey)
    }

    let sessionId: string | undefined
    if (providerKey === "waha") {
      sessionId = await getAuthCookie("waha_session_id")

      if (!sessionId) {
        return {
          status: 401,
          body: ResponseBuilder.fail(
            ["Session ID not found in cookie."],
            [ERROR_CODES.SESSION_REQUIRED],
          ),
        }
      }
    }

    const messages = await provider.receiveMessages(
      sessionId,
      validatedData.conversationId,
      Number(validatedData.limit),
      Number(validatedData.offset),
    )

    return {
      status: 200,
      body: ResponseBuilder.success({
        provider: provider.name,
        session: sessionId,
        messages,
      }),
    }
  } catch (error: any) {
    console.error("Receive messages error:", error)

    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Failed to receive messages. Please try again."],
        [ERROR_CODES.RECEIVE_FAILED],
      ),
    }
  }
}
