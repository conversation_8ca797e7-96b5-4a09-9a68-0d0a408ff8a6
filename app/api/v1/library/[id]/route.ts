import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ERROR_CODES } from "@/app/api/error_codes"
import { ResponseBuilder } from "@/lib/types/responseWrapper"

// GET /api/v1/library/[id] - Get library template by ID
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  const { libraryTemplateBusinessLogic } = getBusinessLogics()
  try {
    const { id } = await params

    if (!id) {
      return NextResponse.json(
        ResponseBuilder.fail(
          ["Template ID is required"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
        { status: 400 },
      )
    }

    const template = await libraryTemplateBusinessLogic.getById(id)

    if (!template) {
      return NextResponse.json(
        ResponseBuilder.fail(
          ["Template not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
        { status: 404 },
      )
    }

    return NextResponse.json(ResponseBuilder.success(template), {
      status: 200,
    })
  } catch (error) {
    console.error("Library template GET by ID route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
