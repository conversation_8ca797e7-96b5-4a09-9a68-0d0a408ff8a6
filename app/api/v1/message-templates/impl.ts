import {
  MessageTemplateBusinessLogicInterface,
  MessageTemplate,
  MessageTemplateCreateInput,
  MessageTemplateUpdateInput,
} from "@/lib/repositories/messageTemplates/interface"
import { ResponseBuilder, ResponseWrapper } from "@/lib/types/responseWrapper"
import {
  MessageTemplateCreateSchema,
  MessageTemplateUpdateSchema,
} from "@/lib/validations/messageTemplate"
import { ERROR_CODES } from "@/app/api/error_codes"
import { SessionContext } from "@/lib/repositories/auth/types"

// Create MessageTemplate Implementation
export async function implHandleCreateMessageTemplate(
  data: any,
  businessLogic: MessageTemplateBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate input data
    const validationResult = MessageTemplateCreateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: ResponseBuilder.fail(errors, [ERROR_CODES.VALIDATION_FAILED]),
      }
    }

    const messageTemplate = await businessLogic.create(
      validationResult.data,
      context,
    )

    return {
      status: 201,
      body: ResponseBuilder.success(messageTemplate),
    }
  } catch (error: any) {
    console.error("Create messageTemplate error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: ResponseBuilder.fail(
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_EMAIL") {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Failed to create messageTemplate. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

// Update MessageTemplate Implementation
export async function implHandleUpdateMessageTemplate(
  id: string,
  data: any,
  businessLogic: MessageTemplateBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Check for empty update object
    if (!data || Object.keys(data).length === 0) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate input data
    const validationResult = MessageTemplateUpdateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: ResponseBuilder.fail(errors, [ERROR_CODES.VALIDATION_FAILED]),
      }
    }

    const messageTemplate = await businessLogic.update(
      id,
      validationResult.data,
      context,
    )

    if (!messageTemplate) {
      return {
        status: 404,
        body: ResponseBuilder.fail(
          ["MessageTemplate not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: ResponseBuilder.success(messageTemplate),
    }
  } catch (error: any) {
    console.error("Update messageTemplate error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: ResponseBuilder.fail(
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Failed to update messageTemplate. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

// Delete MessageTemplate Implementation
export async function implHandleDeleteMessageTemplate(
  id: string,
  businessLogic: MessageTemplateBusinessLogicInterface,
  context: SessionContext,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await businessLogic.delete(id, context, hardDelete)

    if (!success) {
      return {
        status: 404,
        body: ResponseBuilder.fail(
          ["MessageTemplate not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: ResponseBuilder.success({
        message: "MessageTemplate deleted successfully",
      }),
    }
  } catch (error: any) {
    console.error("Delete messageTemplate error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: ResponseBuilder.fail([error.message], [ERROR_CODES.NOT_FOUND]),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Failed to delete messageTemplate. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

// Get MessageTemplate by ID Implementation
export async function implHandleGetMessageTemplate(
  id: string,
  businessLogic: MessageTemplateBusinessLogicInterface,
  context: SessionContext,
  includeDeleted: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const messageTemplate = await businessLogic.getById(
      id,
      context,
      includeDeleted,
    )

    if (!messageTemplate) {
      return {
        status: 404,
        body: ResponseBuilder.fail(
          ["MessageTemplate not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: ResponseBuilder.success(messageTemplate),
    }
  } catch (error: any) {
    console.error("Get messageTemplate error:", error)

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Failed to fetch messageTemplate. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

interface GetAllResultPaginated<T> {
  items: T[]
  page: number
  total: number
}

// Get All MessageTemplate Implementation (handles all, search, and tag filtering)
export async function implHandleGetAllMessageTemplates(
  businessLogic: MessageTemplateBusinessLogicInterface,
  context: SessionContext,
  params: {
    search?: string
    includeDeleted?: boolean
    page?: number
    limit?: number
    sort?: {
      field: keyof MessageTemplate | string
      direction: "ASC" | "DESC"
    }[]
    filters?: {
      field: keyof MessageTemplate | string
      value: MessageTemplate[keyof MessageTemplate] | any
    }[]
  },
): Promise<{
  status: number
  body: ResponseWrapper<GetAllResultPaginated<MessageTemplate>>
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === "") {
        return {
          status: 400,
          body: ResponseBuilder.fail(
            ["Search keyword cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === "") {
          return {
            status: 400,
            body: ResponseBuilder.fail(
              ["Filter field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
      }
    }

    // Validate sort if provided
    if (params?.sort && params.sort.length > 0) {
      for (const sort of params.sort) {
        if (!sort.field || sort.field.trim() === "") {
          return {
            status: 400,
            body: ResponseBuilder.fail(
              ["Sort field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
        if (!["ASC", "DESC"].includes(sort.direction)) {
          return {
            status: 400,
            body: ResponseBuilder.fail(
              ["Sort direction must be 'asc' or 'desc'"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    }

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim()
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters
    }

    // Add sort if provided
    if (params?.sort && params.sort.length > 0) {
      queryParams.sort = params.sort
    }

    const result = await businessLogic.getAll(queryParams, context)

    return {
      status: 200,
      body: ResponseBuilder.success({ ...result, page: queryParams.page }),
    }
  } catch (error: any) {
    console.error("Get messageTemplate error:", error)

    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Failed to fetch messageTemplate. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

// Bulk Create MessageTemplate Implementation
export async function implHandleBulkCreateMessageTemplate(
  data: any[],
  businessLogic: MessageTemplateBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that data is an array
    if (!Array.isArray(data) || data.length === 0) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          ["Input must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each item in the array
    const validatedData: MessageTemplateCreateInput[] = []
    for (let i = 0; i < data.length; i++) {
      const validationResult = MessageTemplateCreateSchema.safeParse(data[i])
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(
          (err) => `Item ${i}: ${err.path.join(".")}: ${err.message}`,
        )
        return {
          status: 400,
          body: ResponseBuilder.fail(errors, [ERROR_CODES.VALIDATION_FAILED]),
        }
      }
      validatedData.push(validationResult.data)
    }

    const messageTemplate = await businessLogic.bulkCreate(
      validatedData,
      context,
    )

    return {
      status: 201,
      body: ResponseBuilder.success(messageTemplate),
    }
  } catch (error: any) {
    console.error("Bulk create messageTemplate error:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: ResponseBuilder.fail(
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Failed to bulk create messageTemplate. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

// Bulk Update MessageTemplate Implementation
export async function implHandleBulkUpdateMessageTemplate(
  updates: { id: string; data: any }[],
  businessLogic: MessageTemplateBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that updates is an array
    if (!Array.isArray(updates) || updates.length === 0) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          ["Updates must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each update in the array
    const validatedUpdates: { id: string; data: MessageTemplateUpdateInput }[] =
      []
    for (let i = 0; i < updates.length; i++) {
      const update = updates[i]

      if (!update.id || typeof update.id !== "string") {
        return {
          status: 400,
          body: ResponseBuilder.fail(
            [`Update ${i}: ID is required and must be a string`],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }

      const validationResult = MessageTemplateUpdateSchema.safeParse(
        update.data,
      )
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(
          (err) => `Update ${i}: ${err.path.join(".")}: ${err.message}`,
        )
        return {
          status: 400,
          body: ResponseBuilder.fail(errors, [ERROR_CODES.VALIDATION_FAILED]),
        }
      }

      validatedUpdates.push({
        id: update.id,
        data: validationResult.data,
      })
    }

    const updatedCount = await businessLogic.bulkUpdate(
      validatedUpdates,
      context,
    )

    return {
      status: 200,
      body: ResponseBuilder.success({ updatedCount }),
    }
  } catch (error: any) {
    console.error("Bulk update messageTemplate error:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: ResponseBuilder.fail(
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Failed to bulk update messageTemplate. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

// Bulk Delete MessageTemplate Implementation
export async function implHandleBulkDeleteMessageTemplate(
  ids: string[],
  businessLogic: MessageTemplateBusinessLogicInterface,
  context: SessionContext,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that ids is an array
    if (!Array.isArray(ids) || ids.length === 0) {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          ["IDs must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each ID
    for (let i = 0; i < ids.length; i++) {
      const id = ids[i]
      if (!id || typeof id !== "string" || id.trim() === "") {
        return {
          status: 400,
          body: ResponseBuilder.fail(
            [`ID at index ${i} is required`],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }

    const deletedCount = await businessLogic.bulkDelete(
      ids,
      context,
      hardDelete,
    )

    return {
      status: 200,
      body: ResponseBuilder.success({ deletedCount }),
    }
  } catch (error: any) {
    console.error("Bulk delete messageTemplate error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: ResponseBuilder.fail([error.message], [ERROR_CODES.NOT_FOUND]),
      }
    }

    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Failed to bulk delete messageTemplate. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

// Restore MessageTemplate Implementation
export async function implHandleRestoreMessageTemplate(
  id: string,
  businessLogic: MessageTemplateBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate ID
    if (!id || typeof id !== "string" || id.trim() === "") {
      return {
        status: 400,
        body: ResponseBuilder.fail(
          ["MessageTemplate ID is required"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const restored = await businessLogic.restore(id, context)

    if (!restored) {
      return {
        status: 404,
        body: ResponseBuilder.fail(
          ["MessageTemplate not found or cannot be restored"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: ResponseBuilder.success({ restored: true }),
    }
  } catch (error: any) {
    console.error("Restore messageTemplate error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: ResponseBuilder.fail([error.message], [ERROR_CODES.NOT_FOUND]),
      }
    }

    return {
      status: 500,
      body: ResponseBuilder.fail(
        ["Failed to restore messageTemplate. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}
