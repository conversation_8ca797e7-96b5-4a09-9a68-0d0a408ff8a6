import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { ResponseBuilder } from "@/lib/types/responseWrapper"

// GET /api/v1/organization/collaborators/[id] - Get collaborator by ID
export async function GET(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { organizationCollaboratorsBusinessLogic } = getBusinessLogics()
    const { id } = await routeContext.params

    const collaborator = await organizationCollaboratorsBusinessLogic.getById(
      id,
      context,
    )

    if (!collaborator) {
      return NextResponse.json(
        ResponseBuilder.fail(
          ["Collaborator not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
        { status: 404 },
      )
    }

    return NextResponse.json(ResponseBuilder.success(collaborator), {
      status: 200,
    })
  } catch (error) {
    console.error("Organization collaborator GET route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

// PUT /api/v1/organization/collaborators/[id] - Update collaborator role
export async function PUT(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { organizationCollaboratorsBusinessLogic } = getBusinessLogics()
    const { id } = await routeContext.params
    const body = await req.json()

    const updatedCollaborator =
      await organizationCollaboratorsBusinessLogic.update(id, body, context)

    if (!updatedCollaborator) {
      return NextResponse.json(
        ResponseBuilder.fail(
          ["Collaborator not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
        { status: 404 },
      )
    }

    return NextResponse.json(ResponseBuilder.success(updatedCollaborator), {
      status: 200,
    })
  } catch (error) {
    console.error("Organization collaborator PUT route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

// DELETE /api/v1/organization/collaborators/[id] - Remove collaborator
export async function DELETE(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { organizationCollaboratorsBusinessLogic } = getBusinessLogics()
    const { id } = await routeContext.params

    const success = await organizationCollaboratorsBusinessLogic.delete(
      id,
      context,
    )

    if (!success) {
      return NextResponse.json(
        ResponseBuilder.fail(
          ["Collaborator not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
        { status: 404 },
      )
    }

    return NextResponse.json(ResponseBuilder.success({ success: true }), {
      status: 200,
    })
  } catch (error) {
    console.error("Organization collaborator DELETE route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
