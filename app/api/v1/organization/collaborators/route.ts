import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { ResponseBuilder } from "@/lib/types/responseWrapper"

// GET /api/v1/organization/collaborators - Get all organization collaborators
export async function GET(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { organizationCollaboratorsBusinessLogic } = getBusinessLogics()

    const url = new URL(req.url)
    const page = parseInt(url.searchParams.get("page") || "1")
    const limit = parseInt(url.searchParams.get("limit") || "10")
    const search = url.searchParams.get("search") || undefined
    const role = url.searchParams.get("role") || undefined
    const status = url.searchParams.get("status") || undefined

    const params = {
      page,
      limit,
      offset: (page - 1) * limit,
      search,
      filters: [
        ...(role ? [{ field: "role", value: role }] : []),
        ...(status ? [{ field: "status", value: status }] : []),
      ],
      sort: [{ field: "createdAt", direction: "DESC" as const }],
    }

    const result = await organizationCollaboratorsBusinessLogic.getAll(
      params,
      context,
    )

    const response_data = {
      ...result,
      page,
      pageSize: limit,
    }

    return NextResponse.json(ResponseBuilder.success(response_data), {
      status: 200,
    })
  } catch (error) {
    console.error("Organization collaborators GET route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
