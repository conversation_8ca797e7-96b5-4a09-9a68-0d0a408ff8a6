import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { ResponseBuilder } from "@/lib/types/responseWrapper"

// POST /api/v1/organization/invitation-link - Generate invitation link
export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const { role = "member" } = body

    // Generate a mock token
    const token = Math.random().toString(36).substring(2, 15) + 
                  Math.random().toString(36).substring(2, 15)
    
    // Get the base URL from the request
    const baseUrl = req.headers.get('origin') || 
                   req.headers.get('host') ? 
                   `${req.headers.get('x-forwarded-proto') || 'http'}://${req.headers.get('host')}` :
                   'http://localhost:3000'

    const invitationLink = `${baseUrl}/invitations/accept?token=${token}`
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days

    const result = {
      invitationLink,
      token,
      expiresAt,
    }

    return NextResponse.json(
      ResponseBuilder.success(result),
      { status: 200 }
    )
  } catch (error) {
    console.error("Organization invitation-link POST route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
      { status: 500 }
    )
  }
}
