import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { ResponseBuilder } from "@/lib/types/responseWrapper"

// GET /api/v1/organization/invitations/[token] - Get invitation info
export async function GET(
  req: NextRequest,
  routeContext: { params: Promise<{ token: string }> },
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { organizationCollaboratorsBusinessLogic } = getBusinessLogics()
    const { token } = await routeContext.params

    const invitationInfo =
      await organizationCollaboratorsBusinessLogic.getInvitationInfo(
        token,
        context,
      )

    return NextResponse.json(ResponseBuilder.success(invitationInfo), {
      status: 200,
    })
  } catch (error) {
    console.error("Organization invitation info GET route error:", error)

    // Handle specific business logic errors
    if (error.code === "INVALID_INVITATION") {
      return NextResponse.json(
        ResponseBuilder.fail([error.message], [ERROR_CODES.NOT_FOUND]),
        { status: 404 },
      )
    }

    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
