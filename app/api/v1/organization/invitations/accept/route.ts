import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { ResponseBuilder } from "@/lib/types/responseWrapper"

// POST /api/v1/organization/invitations/accept - Accept invitation
export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { organizationCollaboratorsBusinessLogic } = getBusinessLogics()
    const body = await req.json()
    const { token } = body

    if (!token) {
      return NextResponse.json(
        ResponseBuilder.fail(
          ["Token is required"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
        { status: 400 },
      )
    }

    const result =
      await organizationCollaboratorsBusinessLogic.acceptInvitation(
        token,
        context,
      )

    return NextResponse.json(ResponseBuilder.success(result), { status: 200 })
  } catch (error) {
    console.error("Organization invitation accept POST route error:", error)

    // Handle specific business logic errors
    if (
      error.code === "INVALID_INVITATION" ||
      error.code === "EXPIRED_INVITATION"
    ) {
      return NextResponse.json(
        ResponseBuilder.fail([error.message], [ERROR_CODES.VALIDATION_FAILED]),
        { status: 400 },
      )
    }

    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
