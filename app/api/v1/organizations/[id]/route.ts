import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import {
  implHandleGetOrganization,
  implHandleUpdateOrganization,
  implHandleDeleteOrganization,
} from "../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

export async function GET(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { organizationsBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const result = await implHandleGetOrganization(
      id,
      organizationsBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Organization GET route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function PUT(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { organizationsBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const body = await req.json()
    const result = await implHandleUpdateOrganization(
      id,
      body,
      organizationsBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Organization PUT route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function DELETE(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { organizationsBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const result = await implHandleDeleteOrganization(
      id,
      organizationsBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Organization DELETE route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
