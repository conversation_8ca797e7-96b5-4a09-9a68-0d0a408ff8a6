import { NextRequest } from "next/server"
import { OrganizationBusinessLogicInterface } from "@/lib/repositories/organizations/interface"
import { SessionContext } from "@/lib/repositories/auth/types"
import { DeprecatedResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import {
  OrganizationCreateSchema,
  OrganizationUpdateSchema,
} from "@/lib/schemas/organizations"

export async function implHandleGetAllOrganizations(
  params: any,
  organizationsBusinessLogic: OrganizationBusinessLogicInterface,
  context: SessionContext,
) {
  try {
    const result = await organizationsBusinessLogic.getAll(params, context)
    return {
      status: 200,
      body: new DeprecatedResponseWrapper("success", result),
    }
  } catch (error: any) {
    console.error("Error in implHandleGetAllOrganizations:", error)
    return {
      status: 500,
      body: new DeprecatedResponseWrapper(
        "failed",
        undefined,
        [error.message || "Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

export async function implHandleGetOrganization(
  id: string,
  organizationsBusinessLogic: OrganizationBusinessLogicInterface,
  context: SessionContext,
) {
  try {
    const organization = await organizationsBusinessLogic.getById(id, context)
    if (!organization) {
      return {
        status: 404,
        body: new DeprecatedResponseWrapper(
          "failed",
          undefined,
          ["Organization not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new DeprecatedResponseWrapper("success", organization),
    }
  } catch (error: any) {
    console.error("Error in implHandleGetOrganization:", error)
    return {
      status: 500,
      body: new DeprecatedResponseWrapper(
        "failed",
        undefined,
        [error.message || "Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

export async function implHandleCreateOrganization(
  body: any,
  organizationsBusinessLogic: OrganizationBusinessLogicInterface,
  context: SessionContext,
) {
  try {
    const validatedData = OrganizationCreateSchema.parse(body)
    const organization = await organizationsBusinessLogic.create(
      validatedData,
      context,
    )

    return {
      status: 201,
      body: new DeprecatedResponseWrapper("success", organization),
    }
  } catch (error: any) {
    console.error("Error in implHandleCreateOrganization:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new DeprecatedResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_ENTRY],
        ),
      }
    }

    return {
      status: 500,
      body: new DeprecatedResponseWrapper(
        "failed",
        undefined,
        [error.message || "Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

export async function implHandleUpdateOrganization(
  id: string,
  body: any,
  organizationsBusinessLogic: OrganizationBusinessLogicInterface,
  context: SessionContext,
) {
  try {
    const validatedData = OrganizationUpdateSchema.parse(body)
    const organization = await organizationsBusinessLogic.update(
      id,
      validatedData,
      context,
    )

    if (!organization) {
      return {
        status: 404,
        body: new DeprecatedResponseWrapper(
          "failed",
          undefined,
          ["Organization not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new DeprecatedResponseWrapper("success", organization),
    }
  } catch (error: any) {
    console.error("Error in implHandleUpdateOrganization:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new DeprecatedResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_ENTRY],
        ),
      }
    }

    return {
      status: 500,
      body: new DeprecatedResponseWrapper(
        "failed",
        undefined,
        [error.message || "Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

export async function implHandleDeleteOrganization(
  id: string,
  organizationsBusinessLogic: OrganizationBusinessLogicInterface,
  context: SessionContext,
) {
  try {
    const deleted = await organizationsBusinessLogic.delete(id, context)

    if (!deleted) {
      return {
        status: 404,
        body: new DeprecatedResponseWrapper(
          "failed",
          undefined,
          ["Organization not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new DeprecatedResponseWrapper("success", { deleted: true }),
    }
  } catch (error: any) {
    console.error("Error in implHandleDeleteOrganization:", error)
    return {
      status: 500,
      body: new DeprecatedResponseWrapper(
        "failed",
        undefined,
        [error.message || "Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}
