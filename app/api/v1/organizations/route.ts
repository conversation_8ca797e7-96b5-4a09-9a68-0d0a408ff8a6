import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import {
  implHandleGetAllOrganizations,
  implHandleCreateOrganization,
} from "./impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

export async function GET(req: NextRequest) {
  try {
    const { organizationsBusinessLogic } = getBusinessLogics()
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { searchParams } = new URL(req.url)

    // Parse basic parameters
    const search = searchParams.get("search") || undefined
    const includeDeleted = searchParams.get("includeDeleted") === "true"
    const page = searchParams.get("page")
      ? parseInt(searchParams.get("page")!)
      : undefined
    const limit = searchParams.get("limit")
      ? parseInt(searchParams.get("limit")!)
      : undefined

    // Parse filters
    const filters: { field: string; value: any }[] = []
    searchParams.forEach((value, key) => {
      if (key.startsWith("filter_")) {
        const field = key.replace("filter_", "")
        filters.push({ field, value })
      }
    })

    // Parse sort
    const sort: { field: string; direction: "ASC" | "DESC" }[] = []
    const sortParam = searchParams.get("sort")
    if (sortParam) {
      const [field, direction] = sortParam.split(":")
      sort.push({
        field,
        direction: (direction?.toUpperCase() as "ASC" | "DESC") || "ASC",
      })
    }

    const params = {
      search,
      includeDeleted,
      page,
      limit,
      filters,
      sort,
    }

    const result = await implHandleGetAllOrganizations(
      params,
      organizationsBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Organizations GET route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function POST(req: NextRequest) {
  const { organizationsBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const result = await implHandleCreateOrganization(
      body,
      organizationsBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Organizations POST route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
