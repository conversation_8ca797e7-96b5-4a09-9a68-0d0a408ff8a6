import { NextRequest, NextResponse } from "next/server"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { ERROR_CODES } from "@/app/api/error_codes"
import { driver } from "@/lib/repositories/LiveRedisDriver"
import { z } from "zod"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { getGlobalAISetting, setGlobalAISetting } from "./getGlobalAiSetting"

const UpdateGlobalAISchema = z.object({
  enabled: z.boolean(),
})

// GET - Get current global AI setting
export async function GET(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const isEnabled = await getGlobalAISetting(context.user.id)

    return NextResponse.json(
      ResponseBuilder.success({
        enabled: isEnabled,
      }),
      { status: 200 },
    )
  } catch (error: any) {
    console.error("Global AI settings GET error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Failed to get global AI setting"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

// PUT - Update global AI setting
export async function PUT(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const validatedData = UpdateGlobalAISchema.parse(body)

    await setGlobalAISetting(context.user.id, validatedData.enabled)

    return NextResponse.json(
      ResponseBuilder.success({
        enabled: validatedData.enabled,
      }),
      { status: 200 },
    )
  } catch (error: any) {
    console.error("Global AI settings PUT error:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        ResponseBuilder.fail(
          error.errors.map((e) => e.message),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
        { status: 400 },
      )
    }

    return NextResponse.json(
      ResponseBuilder.fail(
        ["Failed to update global AI setting"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
