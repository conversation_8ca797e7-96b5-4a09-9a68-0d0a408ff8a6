import { NextRequest, NextResponse } from "next/server"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { createContextLogger } from "@/lib/logging"
import { loggedApiRoute } from "@/lib/logging/middleware"

export const POST = loggedApiRoute(
  async (req: NextRequest, { params }: { params: Promise<{ id: string }> }) => {
    const { testConversationBusinessLogic } = getBusinessLogics()
    try {
      const { context, response } = await buildSessionContext(req)
      if (response) {
        return response
      }

      const { id } = await params

      const logger = createContextLogger({
        component: "test-conversations",
        userId: context.user.id,
        organizationId: context.organization?.id,
      })

      logger.info("Clearing test conversation messages", { conversationId: id })

      // Get the test conversation
      const conversation = await testConversationBusinessLogic.getById(
        id,
        context,
      )
      if (!conversation) {
        return NextResponse.json(
          ResponseBuilder.fail(
            ["Test conversation not found"],
            [ERROR_CODES.NOT_FOUND],
          ),
          { status: 404 },
        )
      }

      const messageCount = conversation.messages.length

      // Clear all messages by updating the conversation with empty messages array
      await testConversationBusinessLogic.update(
        id,
        {
          messages: [],
        },
        context,
      )

      logger.info("Test conversation messages cleared", {
        conversationId: id,
        messagesDeleted: messageCount,
      })

      return NextResponse.json(
        ResponseBuilder.success({
          cleared: true,
          messagesDeleted: messageCount,
        }),
        { status: 200 },
      )
    } catch (error: any) {
      console.error("Test conversation clear error:", error)
      return NextResponse.json(
        ResponseBuilder.fail(
          ["Failed to clear test conversation"],
          [ERROR_CODES.UPDATE_FAILED],
        ),
        { status: 500 },
      )
    }
  },
)
