import { NextRequest, NextResponse } from "next/server"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { TestMessage } from "@/lib/repositories/testConversations"
import { createContextLogger } from "@/lib/logging"
import { loggedApiRoute } from "@/lib/logging/middleware"
import { generateId } from "@/lib/utils/common"

export const POST = loggedApiRoute(
  async (req: NextRequest, { params }: { params: Promise<{ id: string }> }) => {
    const { testConversationBusinessLogic } = getBusinessLogics()
    try {
      const { context, response } = await buildSessionContext(req)
      if (response) {
        return response
      }

      const { id } = await params
      const body = await req.json()

      const logger = createContextLogger({
        component: "test-conversations",
        userId: context.user.id,
        organizationId: context.organization?.id,
      })

      logger.info("Adding message to test conversation", {
        conversationId: id,
        messageType: body.messageType,
        isFromAI: body.isFromAI,
      })

      // Get the test conversation
      const conversation = await testConversationBusinessLogic.getById(
        id,
        context,
      )
      if (!conversation) {
        return NextResponse.json(
          ResponseBuilder.fail(
            ["Test conversation not found"],
            [ERROR_CODES.NOT_FOUND],
          ),
          { status: 404 },
        )
      }

      // Create new message
      const newMessage: TestMessage = {
        id: body.id,
        content: body.content,
        senderId: body.senderId,
        senderName: body.senderName,
        isFromCustomer: body.isFromCustomer || false,
        isFromAI: body.isFromAI || false,
        timestamp: body.timestamp || new Date().toISOString(),
        messageType: body.messageType || "TEXT",
        metadata: body.metadata || {},
      }

      // Add message to conversation
      const updatedMessages = [...conversation.messages, newMessage]

      // Update the conversation with new message
      const updatedConversation = await testConversationBusinessLogic.update(
        id,
        {
          messages: updatedMessages,
        },
        context,
      )

      logger.info("Message added to test conversation", {
        conversationId: id,
        messageId: newMessage.id,
        totalMessages: updatedMessages.length,
      })

      return NextResponse.json(
        ResponseBuilder.success({
          message: newMessage,
          conversation: updatedConversation,
        }),
        { status: 201 },
      )
    } catch (error: any) {
      console.error("Add test message error:", error)
      return NextResponse.json(
        ResponseBuilder.fail(
          ["Failed to add message to test conversation"],
          [ERROR_CODES.CREATE_FAILED],
        ),
        { status: 500 },
      )
    }
  },
)

export const GET = loggedApiRoute(
  async (req: NextRequest, { params }: { params: Promise<{ id: string }> }) => {
    const { testConversationBusinessLogic } = getBusinessLogics()
    try {
      const { context, response } = await buildSessionContext(req)
      if (response) {
        return response
      }

      const { id } = await params

      const logger = createContextLogger({
        component: "test-conversations",
        userId: context.user.id,
        organizationId: context.organization?.id,
      })

      logger.info("Fetching messages for test conversation", {
        conversationId: id,
      })

      // Get the test conversation
      const conversation = await testConversationBusinessLogic.getById(
        id,
        context,
      )
      if (!conversation) {
        return NextResponse.json(
          ResponseBuilder.fail(
            ["Test conversation not found"],
            [ERROR_CODES.NOT_FOUND],
          ),
          { status: 404 },
        )
      }

      return NextResponse.json(
        ResponseBuilder.success(conversation.messages),
        { status: 200 },
      )
    } catch (error: any) {
      console.error("Get test messages error:", error)
      return NextResponse.json(
        ResponseBuilder.fail(
          ["Failed to fetch messages"],
          [ERROR_CODES.FETCH_FAILED],
        ),
        { status: 500 },
      )
    }
  },
)
