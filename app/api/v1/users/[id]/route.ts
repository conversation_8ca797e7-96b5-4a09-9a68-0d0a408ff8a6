import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import {
  implHandleGetUser,
  implHandleUpdateUser,
  implHandleDeleteUser,
} from "../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { ResponseBuilder } from "@/lib/types/responseWrapper"

export async function GET(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { usersBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const result = await implHandleGetUser(id, usersBusinessLogic, context)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("user GET route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

export async function PUT(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { usersBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const body = await req.json()
    const result = await implHandleUpdateUser(
      id,
      body,
      usersBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("user PUT route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

export async function DELETE(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { usersBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const result = await implHandleDeleteUser(id, usersBusinessLogic, context)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("user DELETE route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
