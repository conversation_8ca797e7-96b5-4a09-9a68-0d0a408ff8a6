//DatasourceForm.tsx
"use client"

import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ArrowLeft,
  Save,
  FileText,
  Globe,
  Database,
  Eye,
  EyeOff,
} from "lucide-react"
import { Datasource } from "@/lib/repositories/datasources/interface"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

interface DatasourceFormProps {
  initialDatasource?: Datasource | null
  onSave: (data: {
    name: string
    type: string
    content?: string
    url?: string
    accessKey?: string
    isActive: boolean
  }) => Promise<void>
  onCancel: () => void
  isSubmitting?: boolean
  submitButtonText?: string
  title: string
  description: string
}

export default function DatasourceForm({
  initialDatasource,
  onSave,
  onCancel,
  isSubmitting = false,
  submitButtonText,
  title,
  description,
}: DatasourceFormProps) {
  const { t } = useLocalization("datasource", locales)

  const [showAccessKey, setShowAccessKey] = useState(false)

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    type: "TEXT",
    content: "",
    url: "",
    accessKey: "",
    isActive: true,
  })

  // Initialize from props
  useEffect(() => {
    if (initialDatasource) {
      setFormData({
        name: initialDatasource.name || "",
        type: initialDatasource.type || "TEXT",
        content: initialDatasource.content || "",
        url: initialDatasource.url || "",
        accessKey: initialDatasource.accessKey || "",
        isActive: initialDatasource.isActive ?? true,
      })
    }
  }, [initialDatasource])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim()) {
      alert(t("form.validation.name_required"))
      return
    }

    if (formData.type === "TEXT" && !formData.content.trim()) {
      alert(t("form.validation.content_required"))
      return
    }

    if (formData.type !== "TEXT" && !formData.url.trim()) {
      alert(t("form.validation.url_required"))
      return
    }

    try {
      await onSave({
        name: formData.name.trim(),
        type: formData.type,
        content: formData.type === "TEXT" ? formData.content.trim() : undefined,
        url: formData.type !== "TEXT" ? formData.url.trim() : undefined,
        accessKey: formData.accessKey.trim() || undefined,
        isActive: formData.isActive,
      })
    } catch (error) {
      console.error("Error saving datasource:", error)
      alert(t("form.validation.save_failed"))
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "TEXT":
        return <FileText className="h-5 w-5" />
      case "API":
        return <Globe className="h-5 w-5" />
      default:
        return <Database className="h-5 w-5" />
    }
  }

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" onClick={onCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{title}</h1>
          <p className="text-gray-600">{description}</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getTypeIcon(formData.type)}
              {t("form.section.details_title")}
            </CardTitle>
            <CardDescription>{t("form.section.details_desc")}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t("form.fields.name.label")}</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder={t("form.fields.name.placeholder")}
                maxLength={100}
                required
              />
              <p className="text-sm text-gray-500">
                {formData.name.length}/100 {t("form.common.characters")}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">{t("form.fields.type.label")}</Label>
              <Select
                value={formData.type}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, type: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={t("form.fields.type.placeholder")}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="TEXT">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      {t("form.fields.type.text")}
                    </div>
                  </SelectItem>
                  {/* Jika nanti jenis lain diaktifkan, tinggal un-comment:
                  <SelectItem value="API">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      {t("form.fields.type.api")}
                    </div>
                  </SelectItem>
                  <SelectItem value="DATABASE">
                    <div className="flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      {t("form.fields.type.database")}
                    </div>
                  </SelectItem> */}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Content/URL Section */}
        <Card>
          <CardHeader>
            <CardTitle>
              {formData.type === "TEXT"
                ? t("form.section.content_title")
                : t("form.section.connection_title")}
            </CardTitle>
            <CardDescription>
              {formData.type === "TEXT"
                ? t("form.section.content_desc")
                : t("form.section.connection_desc")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.type === "TEXT" ? (
              <div className="space-y-2">
                <Label htmlFor="content">
                  {t("form.fields.content.label")}
                </Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      content: e.target.value,
                    }))
                  }
                  placeholder={t("form.fields.content.placeholder")}
                  className="min-h-40"
                  maxLength={10000}
                  required
                />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>
                    {formData.content.length}/10,000{" "}
                    {t("form.common.characters")}
                  </span>
                  <span
                    className={
                      formData.content.length > 9000 ? "text-orange-500" : ""
                    }
                  >
                    {formData.content.length > 9000 &&
                      t("form.common.approaching_limit")}
                  </span>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <Label htmlFor="url">{t("form.fields.url.label")}</Label>
                <Input
                  id="url"
                  type="url"
                  value={formData.url}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, url: e.target.value }))
                  }
                  placeholder={t("form.fields.url.placeholder")}
                  required
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="accessKey">
                {t("form.fields.access_key.label")}
              </Label>
              <div className="relative">
                <Input
                  id="accessKey"
                  type={showAccessKey ? "text" : "password"}
                  value={formData.accessKey}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      accessKey: e.target.value,
                    }))
                  }
                  placeholder={t("form.fields.access_key.placeholder")}
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowAccessKey(!showAccessKey)}
                >
                  {showAccessKey ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit */}
        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            {t("form.buttons.cancel")}
          </Button>
          <Button
            type="submit"
            disabled={
              isSubmitting ||
              !formData.name.trim() ||
              (formData.type === "TEXT" && !formData.content.trim()) ||
              (formData.type !== "TEXT" && !formData.url.trim())
            }
            className="flex items-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {t("form.buttons.saving")}
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                {submitButtonText ?? t("form.buttons.save")}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
