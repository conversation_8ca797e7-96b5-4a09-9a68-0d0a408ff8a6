//DatasourceView.tsx
"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  FileText,
  Globe,
  Database,
  Edit,
  Trash2,
} from "lucide-react"
import { Datasource } from "@/lib/repositories/datasources/interface"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

interface DatasourceViewProps {
  datasource: Datasource
  onEdit: () => void
  onDelete: () => void
  onBack: () => void
}

export default function DatasourceView({
  datasource,
  onEdit,
  onDelete,
  onBack,
}: DatasourceViewProps) {
  const { t } = useLocalization("datasource", locales)

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "TEXT":
        return <FileText className="h-5 w-5" />
      case "API":
        return <Globe className="h-5 w-5" />
      default:
        return <Database className="h-5 w-5" />
    }
  }

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleString(undefined, {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <div className="flex items-center gap-2">
              {getTypeIcon(datasource.type)}
              <h1 className="text-3xl font-bold">{datasource.name}</h1>
              <Badge variant={datasource.isActive ? "default" : "secondary"}>
                {datasource.isActive
                  ? t("view.status.active")
                  : t("view.status.inactive")}
              </Badge>
            </div>
            <p className="text-gray-600">{t("view.header.subtitle")}</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={onEdit}>
            <Edit className="h-4 w-4 mr-2" />
            {t("view.buttons.edit")}
          </Button>
          <Button variant="destructive" onClick={onDelete}>
            <Trash2 className="h-4 w-4 mr-2" />
            {t("view.buttons.delete")}
          </Button>
        </div>
      </div>

      {/* View Mode */}
      <div className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>{t("view.basic.title")}</CardTitle>
            <CardDescription>{t("view.basic.desc")}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-500">
                  {t("form.fields.name.label")}
                </Label>
                <p className="text-lg">{datasource.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">
                  {t("form.fields.type.label")}
                </Label>
                <div className="flex items-center gap-2">
                  {getTypeIcon(datasource.type)}
                  <Badge variant="outline">
                    {t(`form.fields.type.${datasource.type.toLowerCase()}`)}
                  </Badge>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">
                  {t("view.basic.status")}
                </Label>
                <Badge variant={datasource.isActive ? "default" : "secondary"}>
                  {datasource.isActive
                    ? t("view.status.active")
                    : t("view.status.inactive")}
                </Badge>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">
                  {t("view.basic.created")}
                </Label>
                <p>{formatDate(datasource.createdAt)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Content/URL */}
        <Card>
          <CardHeader>
            <CardTitle>
              {datasource.type === "TEXT"
                ? t("view.content.title")
                : t("view.connection.title")}
            </CardTitle>
            <CardDescription>
              {datasource.type === "TEXT"
                ? t("view.content.desc")
                : t("view.connection.desc")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {datasource.type === "TEXT" ? (
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-500">
                  {t("form.fields.content.label")}
                </Label>
                <div className="p-4 bg-gray-50 rounded-md border">
                  <pre className="whitespace-pre-wrap text-sm">
                    {datasource.content || t("view.content.no_content")}
                  </pre>
                </div>
                <p className="text-sm text-gray-500">
                  {datasource.content?.length || 0}{" "}
                  {t("form.common.characters")}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    {t("form.fields.url.label")}
                  </Label>
                  <p className="text-blue-600 break-all">
                    {datasource.url || t("view.connection.no_url")}
                  </p>
                </div>
                {datasource.accessKey && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">
                      {t("form.fields.access_key.label")}
                    </Label>
                    <p className="font-mono text-sm">{"*".repeat(20)}</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Card>
          <CardHeader>
            <CardTitle>{t("view.additional.title")}</CardTitle>
            <CardDescription>{t("view.additional.desc")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-500">
                  {t("view.additional.created_at")}
                </Label>
                <p>{formatDate(datasource.createdAt)}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">
                  {t("view.additional.updated_at")}
                </Label>
                <p>{formatDate(datasource.updatedAt)}</p>
              </div>
              {datasource.createdBy && (
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    {t("view.additional.created_by")}
                  </Label>
                  <p>{datasource.createdBy}</p>
                </div>
              )}
              {datasource.updatedBy && (
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    {t("view.additional.updated_by")}
                  </Label>
                  <p>{datasource.updatedBy}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
