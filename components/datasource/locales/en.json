{"form": {"buttons": {"cancel": "Cancel", "save": "Save", "saving": "Saving..."}, "common": {"approaching_limit": "Approaching limit", "characters": "characters"}, "fields": {"access_key": {"label": "Access Key (Optional)", "placeholder": "Enter access key if required"}, "content": {"label": "Content", "placeholder": "Enter text content here..."}, "name": {"label": "Name", "placeholder": "Enter datasource name"}, "type": {"api": "API Endpoint", "database": "Database", "label": "Type", "placeholder": "Select type", "text": "Text Content"}, "url": {"label": "URL", "placeholder": "https://example.com/api"}}, "section": {"connection_desc": "Enter connection details for this datasource", "connection_title": "Connection Details", "content_desc": "Enter text content for this datasource", "content_title": "Content", "details_desc": "Enter basic information for your datasource", "details_title": "Datasource Details"}, "validation": {"content_required": "Content is required for TEXT type", "name_required": "Name is required", "save_failed": "Failed to save datasource. Try again.", "url_required": "URL is required for non-TEXT types"}}, "view": {"additional": {"created_at": "Created At", "created_by": "Created By", "desc": "Timestamps and metadata", "title": "Additional Information", "updated_at": "Updated At", "updated_by": "Updated By"}, "basic": {"created": "Created", "desc": "Datasource details and metadata", "name": "Name", "status": "Status", "title": "Basic Information", "type": "Type"}, "buttons": {"delete": "Delete", "edit": "Edit"}, "connection": {"access_key": "Access Key", "desc": "Connection information for this datasource", "no_url": "No URL", "title": "Connection Details", "url": "URL"}, "content": {"desc": "Text content stored in this datasource", "label": "Content", "no_content": "No content", "title": "Content"}, "header": {"subtitle": "View datasource details"}, "status": {"active": "Active", "inactive": "Inactive"}}}