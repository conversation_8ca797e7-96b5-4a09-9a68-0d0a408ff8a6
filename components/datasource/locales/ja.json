{"form": {"buttons": {"cancel": "キャンセル", "save": "保存", "saving": "保存中..."}, "common": {"approaching_limit": "制限に近づいています", "characters": "文字"}, "fields": {"access_key": {"label": "アクセスキー (オプション)", "placeholder": "必要に応じてアクセスキーを入力してください"}, "content": {"label": "コンテンツ", "placeholder": "ここにテキストコンテンツを入力してください..."}, "name": {"label": "名前", "placeholder": "データソース名を入力してください"}, "type": {"api": "APIエンドポイント", "database": "データベース", "label": "タイプ", "placeholder": "タイプを選択してください", "text": "テキストコンテンツ"}, "url": {"label": "URL", "placeholder": "https://example.com/api"}}, "section": {"connection_desc": "このデータソースの接続詳細を入力してください", "connection_title": "接続詳細", "content_desc": "このデータソースのテキストコンテンツを入力してください", "content_title": "コンテンツ", "details_desc": "データソースの基本情報を入力してください", "details_title": "データソース詳細"}, "validation": {"content_required": "TEXTタイプにはコンテンツが必要です", "name_required": "名前が必要です", "save_failed": "データソースの保存に失敗しました。もう一度お試しください。", "url_required": "非TEXTタイプにはURLが必要です"}}, "view": {"additional": {"created_at": "作成日時", "created_by": "作成者", "desc": "タイムスタンプとメタデータ", "title": "追加情報", "updated_at": "更新日時", "updated_by": "更新者"}, "basic": {"created": "作成", "desc": "データソースの詳細とメタデータ", "name": "名前", "status": "ステータス", "title": "基本情報", "type": "タイプ"}, "buttons": {"delete": "削除", "edit": "編集"}, "connection": {"access_key": "アクセスキー", "desc": "このデータソースの接続情報", "no_url": "URLなし", "title": "接続詳細", "url": "URL"}, "content": {"desc": "このデータソースに保存されたテキストコンテンツ", "label": "コンテンツ", "no_content": "コンテンツなし", "title": "コンテンツ"}, "header": {"subtitle": "データソースの詳細を表示"}, "status": {"active": "アクティブ", "inactive": "非アクティブ"}}}