"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Copy, Check, Link } from "lucide-react"
import { CollaborationAPI } from "@/lib/services/collaborationApi"

interface InvitationLinkDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  t: (key: string) => string
}

export function InvitationLinkDialog({
  open,
  onOpenChange,
  t,
}: InvitationLinkDialogProps) {
  const [role, setRole] = useState("member")
  const [invitationLink, setInvitationLink] = useState("")
  const [loading, setLoading] = useState(false)
  const [copied, setCopied] = useState(false)
  const [error, setError] = useState("")

  const generateLink = async () => {
    try {
      setLoading(true)
      setError("")
      const result = await CollaborationAPI.GenerateInvitationLink({ role }).request()
      setInvitationLink(result.invitationLink)
    } catch (err: any) {
      console.error("Error generating invitation link:", err)
      setError(t("generate_link_error"))
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(invitationLink)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error("Failed to copy:", err)
    }
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      // Reset state when closing
      setRole("member")
      setInvitationLink("")
      setError("")
      setCopied(false)
    }
    onOpenChange(newOpen)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Link className="h-5 w-5" />
            {t("generate_invitation_link")}
          </DialogTitle>
          <DialogDescription>
            {t("invitation_link_description")}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="role">{t("member_role")}</Label>
            <Select value={role} onValueChange={setRole}>
              <SelectTrigger>
                <SelectValue placeholder={t("select_role")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="member">{t("role_member")}</SelectItem>
                <SelectItem value="admin">{t("role_admin")}</SelectItem>
                <SelectItem value="editor">{t("role_editor")}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {!invitationLink ? (
            <Button
              onClick={generateLink}
              disabled={loading}
              className="w-full"
            >
              {loading ? t("generating") : t("generate_link")}
            </Button>
          ) : (
            <div className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="invitation-link">{t("invitation_link")}</Label>
                <div className="flex space-x-2">
                  <Input
                    id="invitation-link"
                    value={invitationLink}
                    readOnly
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={copyToClipboard}
                    className="shrink-0"
                  >
                    {copied ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {copied && (
                <Alert>
                  <Check className="h-4 w-4" />
                  <AlertDescription>{t("link_copied")}</AlertDescription>
                </Alert>
              )}

              <Alert>
                <AlertDescription>
                  {t("invitation_link_expires")}
                </AlertDescription>
              </Alert>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => handleOpenChange(false)}>
            {t("close")}
          </Button>
          {invitationLink && (
            <Button onClick={() => generateLink()} disabled={loading}>
              {t("generate_new_link")}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
