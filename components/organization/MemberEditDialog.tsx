"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { User } from "lucide-react"
import { Collaborator, CollaborationAPI } from "@/lib/services/collaborationApi"

interface MemberEditDialogProps {
  member: Collaborator | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onMemberUpdated: () => void
  t: (key: string) => string
}

export function MemberEditDialog({
  member,
  open,
  onOpenChange,
  onMemberUpdated,
  t,
}: MemberEditDialogProps) {
  const [role, setRole] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  useEffect(() => {
    if (member) {
      setRole(member.role)
    }
  }, [member])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!member) return

    try {
      setLoading(true)
      setError("")
      setSuccess("")

      await CollaborationAPI.UpdateCollaboratorRole({
        collaboratorId: member.id,
        role,
      }).request()

      setSuccess(t("member_updated_success"))
      onMemberUpdated()
      
      // Close dialog after a short delay
      setTimeout(() => {
        onOpenChange(false)
      }, 1500)
    } catch (err: any) {
      console.error("Error updating member:", err)
      setError(t("member_update_error"))
    } finally {
      setLoading(false)
    }
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      // Reset state when closing
      setRole("")
      setError("")
      setSuccess("")
    }
    onOpenChange(newOpen)
  }

  if (!member) return null

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {t("edit_member")}
          </DialogTitle>
          <DialogDescription>
            {t("edit_member_description")}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="member-email">{t("email")}</Label>
            <Input
              id="member-email"
              value={member.email}
              disabled
              className="bg-gray-50"
            />
            <p className="text-sm text-gray-500">{t("email_readonly")}</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="member-role">{t("role")}</Label>
            <Select value={role} onValueChange={setRole}>
              <SelectTrigger>
                <SelectValue placeholder={t("select_role")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="member">{t("role_member")}</SelectItem>
                <SelectItem value="admin">{t("role_admin")}</SelectItem>
                <SelectItem value="editor">{t("role_editor")}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>{t("status")}</Label>
            <div className="text-sm text-gray-600">
              {member.status === "active" && t("status_active")}
              {member.status === "invited" && t("status_invited")}
              {member.status === "declined" && t("status_declined")}
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={loading}
            >
              {t("cancel")}
            </Button>
            <Button type="submit" disabled={loading || role === member.role}>
              {loading ? t("saving") : t("save_changes")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
