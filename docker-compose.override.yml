# This file is automatically loaded by docker-compose for development overrides
# To disable this file for production deployments, use: docker compose -f docker-compose.yml

services:
  cs-ai-app:
    # Override for development - use volume mounts for hot reloading
    build:
      target: builder # Use builder stage for development (has all dependencies)
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
    ports:
      - "3000:3000"
    # Override command for development
    command: ["bun", "run", "dev"]
    # Remove healthcheck for development to avoid unnecessary overhead
    healthcheck:
      disable: true
