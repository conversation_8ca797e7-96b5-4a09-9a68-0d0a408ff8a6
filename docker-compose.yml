# Docker Compose file for CS AI App

services:
  cs-ai-app:
    # Image can be overridden via CS_AI_APP_IMAGE environment variable
    # Examples:
    #   CS_AI_APP_IMAGE=cs-ai-app:v1.2.3 docker compose up -d
    #   make deploy TAG=cs-ai-app:v1.2.3
    image: ${CS_AI_APP_IMAGE:-cs-ai-app:latest}
    build:
      context: .
      dockerfile: Dockerfile
      target: runner # Use production stage
    container_name: ${CS_AI_CONTAINER_NAME:-cs-ai-app}
    ports:
      - "${DOCKER_EXPOSED_PORT:-3000}:3000"
    env_file:
      - ${CS_AI_ENV_FILE:-.env}
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
    restart: unless-stopped
    networks:
      - cs-ai-network
    volumes:
      # Mount logs directory for application logs
      - ./logs:/app/logs:rw

    # Add this block to allow containers to resolve host.docker.internal (Linux only)
    extra_hosts:
      # On Linux, this maps 'host.docker.internal' to the special gateway IP for the Docker host.
      # This makes it possible to access services running on the host machine (e.g., <PERSON><PERSON>, <PERSON>, etc.)
      - "host.docker.internal:host-gateway"

    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:3000/api/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: "0.5"
        reservations:
          memory: 512M
          cpus: "0.25"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  cs-ai-network:
    driver: bridge
