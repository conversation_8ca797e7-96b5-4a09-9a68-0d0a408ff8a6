#!/bin/bash

# CS AI App Docker Startup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status()    { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success()   { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning()   { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error()     { echo -e "${RED}[ERROR]${NC} $1"; }

# Parse flags
DETACHED=""
FORCE_BUILD=false
USE_IMAGE_TAG=""
ENV_FILE=".env"

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--detached)   DETACHED="-d"; shift ;;
        -b|--build)      FORCE_BUILD=true; shift ;;
        -t|--tag)        USE_IMAGE_TAG="$2"; shift 2 ;;
        -e|--env)        ENV_FILE="$2"; shift 2 ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "  -d, --detached      Run in detached mode"
            echo "  -b, --build         Force rebuild of images"
            echo "  -t, --tag TAG       Use specific image tag (default: latest)"
            echo "  -e, --env FILE      Use specific environment file (default: .env)"
            echo "  -h, --help          Show this help message"
            echo ""
            echo "Environment variables:"
            echo "  CS_AI_APP_IMAGE     Image tag to use (alternative to -t flag)"
            echo ""
            echo "Examples:"
            echo "  $0 -d                           # Deploy latest image in detached mode"
            echo "  $0 -t cs-ai-app:v1.2.3 -d      # Deploy specific version"
            echo "  $0 -e .env.staging -d           # Deploy using staging environment"
            echo "  $0 -b -d                        # Build and deploy"
            exit 0
            ;;
        *) print_error "Unknown option: $1"; exit 1 ;;
    esac
done

# Ensure environment file exists
print_status "Using environment file: $ENV_FILE"
if [ ! -f "$ENV_FILE" ]; then
    print_warning "$ENV_FILE file not found!"
    if [ "$ENV_FILE" = ".env" ] && [ -f ".env.example" ]; then
        print_status "Copying .env.example to $ENV_FILE..."
        cp .env.example "$ENV_FILE"
        print_warning "Please edit $ENV_FILE before running."
        exit 1
    else
        print_error "Environment file $ENV_FILE not found!"
        print_status "Available .env files:"
        ls -la .env* 2>/dev/null || echo "  No .env files found"
        exit 1
    fi
fi

# Load environment variables to get the exposed port
if [ -f "$ENV_FILE" ]; then
    export $(grep -v '^#' "$ENV_FILE" | xargs)
fi

# Set default port if not specified
EXPOSED_PORT=${DOCKER_EXPOSED_PORT:-3000}

# Use CS_AI_APP_IMAGE environment variable if set and no -t flag provided
if [ -n "$CS_AI_APP_IMAGE" ] && [ -z "$USE_IMAGE_TAG" ]; then
    USE_IMAGE_TAG="$CS_AI_APP_IMAGE"
fi

# Default to latest if no image specified
if [ -z "$USE_IMAGE_TAG" ]; then
    USE_IMAGE_TAG="$(basename $(pwd)):latest"
fi

print_status "Starting CS AI App..."
print_status "Application will be available on port: $EXPOSED_PORT"

# Use single compose file (explicitly specify to avoid override file when deploying)
COMPOSE_FILE="docker-compose.yml"
COMPOSE_FLAGS="-f $COMPOSE_FILE"
print_status "Using compose file: $COMPOSE_FILE"

# Ensure Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Start it and try again."
    exit 1
fi

# Handle image building or selection
if [ "$FORCE_BUILD" = true ]; then
    print_status "Building Docker images..."
    docker compose --env-file "$ENV_FILE" $COMPOSE_FLAGS build
    print_success "Build complete!"
    # Use the built image (latest tag)
    export CS_AI_APP_IMAGE="$(basename $(pwd)):latest"
else
    print_status "Using specified image tag: $USE_IMAGE_TAG"
    # Check if the image exists
    if ! docker image inspect "$USE_IMAGE_TAG" >/dev/null 2>&1; then
        print_error "Image $USE_IMAGE_TAG not found!"
        print_status "Available images:"
        docker images --filter "reference=$(basename $(pwd))" --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}"
        exit 1
    fi
    # Export the image tag for docker-compose
    export CS_AI_APP_IMAGE="$USE_IMAGE_TAG"
fi

# Stop existing containers
print_status "Stopping existing containers..."
docker compose --env-file "$ENV_FILE" $COMPOSE_FLAGS down

# Start containers
print_status "Starting containers..."
docker compose --env-file "$ENV_FILE" $COMPOSE_FLAGS up $DETACHED

if [ -n "$DETACHED" ]; then
    print_success "Application started in detached mode!"
    print_status "Visit: http://localhost:$EXPOSED_PORT"
    print_status "Environment: $(grep APP_ENVIRONMENT "$ENV_FILE" 2>/dev/null | cut -d'=' -f2 || echo "unknown")"
    print_status "Config file: $ENV_FILE"
    print_status "To view logs: docker compose --env-file $ENV_FILE $COMPOSE_FLAGS logs -f"
    print_status "To stop: docker compose --env-file $ENV_FILE $COMPOSE_FLAGS down"
else
    print_success "Application running. Press Ctrl+C to stop."
    print_status "Visit: http://localhost:$EXPOSED_PORT"
    print_status "Environment: $(grep APP_ENVIRONMENT "$ENV_FILE" 2>/dev/null | cut -d'=' -f2 || echo "unknown")"
fi
