"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { Organization } from "@/lib/repositories/organizations/interface"
import { OrganizationsAPI } from "@/lib/services/organizationsApi"
import { StorageKeys, secureStorage } from "@/lib/utils/SecureStorage"

interface OrganizationContextType {
  organizations: Organization[]
  currentOrganization: Organization | null
  loading: boolean
  error: string | null
  setCurrentOrganization: (organization: Organization | null) => void
  refreshOrganizations: () => Promise<void>
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined)

export function useOrganization() {
  const context = useContext(OrganizationContext)
  if (context === undefined) {
    throw new Error("useOrganization must be used within an OrganizationProvider")
  }
  return context
}

interface OrganizationProviderProps {
  children: ReactNode
  initialOrganizations?: Organization[]
}

export function OrganizationProvider({
  children,
  initialOrganizations = []
}: OrganizationProviderProps) {
  const [organizations, setOrganizations] = useState<Organization[]>(initialOrganizations)
  const [currentOrganization, setCurrentOrganizationState] = useState<Organization | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadCurrentOrganization = () => {
      try {
        const storedOrgId = secureStorage.getItem(StorageKeys.CurrentOrganization)
        if (storedOrgId) {
          const existingOrg = organizations.find(org => org.id === storedOrgId)
          if (existingOrg) {
            setCurrentOrganizationState(existingOrg)
          } else {
            secureStorage.removeItem(StorageKeys.CurrentOrganization)
          }
        }
      } catch (error) {
        console.error("Error loading current organization from storage:", error)
        secureStorage.removeItem(StorageKeys.CurrentOrganization)
      }
    }

    if (organizations.length > 0) {
      loadCurrentOrganization()
    }
  }, [organizations])

  const setCurrentOrganization = (organization: Organization | null) => {
    setCurrentOrganizationState(organization)

    if (organization) {
      secureStorage.setItem(StorageKeys.CurrentOrganization, organization.id)
    } else {
      secureStorage.removeItem(StorageKeys.CurrentOrganization)
    }

    window.location.reload()
  }

  const refreshOrganizations = async () => {
    try {
      setLoading(true)
      setError(null)

      const result = await OrganizationsAPI.All({
        page: 1,
        limit: 100, // Get all organizations for the user
      }).request()

      setOrganizations(result.items || [])

      // If current organization is no longer in the list, clear it
      if (currentOrganization) {
        const stillExists = result.items?.find(org => org.id === currentOrganization.id)
        if (!stillExists) {
          setCurrentOrganization(null)
        }
      }
    } catch (err: any) {
      console.error("Error refreshing organizations:", err)
      setError(err.message || "Failed to load organizations")
    } finally {
      setLoading(false)
    }
  }

  // Load organizations on mount if not provided initially
  useEffect(() => {
    if (initialOrganizations.length === 0) {
      refreshOrganizations()
    }
  }, [])

  const value: OrganizationContextType = {
    organizations,
    currentOrganization,
    loading,
    error,
    setCurrentOrganization,
    refreshOrganizations,
  }

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  )
}
