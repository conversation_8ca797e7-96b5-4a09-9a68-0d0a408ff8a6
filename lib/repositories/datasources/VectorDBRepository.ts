import { Pinecone, Index, RecordMetadata } from "@pinecone-database/pinecone"
import { SessionContext } from "../auth/types"
import { __getNamespace } from "../BaseVectorDBRepository"

export interface DatasourceVectorDB {
  id: string
  content: string
}

export interface DatasourceVectorDBRepository {
  create(item: DatasourceVectorDB, context: SessionContext): Promise<void>
  update(
    id: string,
    item: DatasourceVectorDB,
    context: SessionContext,
  ): Promise<void>
  delete(id: string, context: SessionContext): Promise<void>
  search(
    text: string,
    topK: number,
    context: SessionContext,
  ): Promise<DatasourceVectorDB[]>
  searchSimilar(
    query: string,
    topK: number,
    context: SessionContext,
  ): Promise<DatasourceVectorDB[]>
  searchByKeywords(
    keywords: string[],
    topK: number,
    context: SessionContext,
  ): Promise<DatasourceVectorDB[]>
}

export class PineconeDatasourceVectorDBRepository
  implements DatasourceVectorDBRepository
{
  private pinecone: Pinecone

  constructor(pineconeApiKey: string) {
    this.pinecone = new Pinecone({ apiKey: pineconeApiKey })
  }

  private getNamespace(context: SessionContext): Index<RecordMetadata> {
    const namespace = __getNamespace(context)
    return this.pinecone.index("datasources").namespace(namespace)
  }

  async create(
    item: DatasourceVectorDB,
    context: SessionContext,
  ): Promise<void> {
    const namespace = this.getNamespace(context)
    await namespace.upsertRecords([
      {
        _id: item.id,
        text: item.content,
      },
    ])
  }

  async update(
    id: string,
    item: DatasourceVectorDB,
    context: SessionContext,
  ): Promise<void> {
    const namespace = this.getNamespace(context)
    await namespace.upsertRecords([
      {
        _id: id,
        text: item.content,
      },
    ])
  }

  async delete(id: string, context: SessionContext): Promise<void> {
    const namespace = this.getNamespace(context)
    await namespace.deleteMany([id])
  }

  async search(
    text: string,
    topK: number = 5,
    context: SessionContext,
  ): Promise<DatasourceVectorDB[]> {
    const namespace = this.getNamespace(context)
    const searchResult = await namespace.searchRecords({
      query: {
        topK,
        inputs: { text: text }, // this gets embedded automatically
      },
    })

    return (searchResult.result.hits || []).map((hit: any) => {
      const metadata = hit.fields as any

      return {
        id: hit._id,
        content: metadata?.text || "",
      }
    })
  }

  async searchSimilar(
    query: string,
    topK: number = 5,
    context: SessionContext,
  ): Promise<DatasourceVectorDB[]> {
    return this.search(query, topK, context)
  }

  async searchByKeywords(
    keywords: string[],
    topK: number = 5,
    context: SessionContext,
  ): Promise<DatasourceVectorDB[]> {
    const keywordQuery = keywords.join(" ")
    return this.search(keywordQuery, topK, context)
  }
}
