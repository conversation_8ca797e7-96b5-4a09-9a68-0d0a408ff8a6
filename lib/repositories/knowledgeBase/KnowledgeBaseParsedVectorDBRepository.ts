import { Pinecone, Index, RecordMetadata } from "@pinecone-database/pinecone"
import { SessionContext } from "../auth/types"
import { __getNamespace } from "../BaseVectorDBRepository"

export interface KnowledgeBaseParsedVectorDB {
  id: string
  content: string
  title: string
  keywords: string[]
  category?: string
  tags?: string[]
}

export interface KnowledgeBaseParsedVectorDBRepository {
  create(
    item: KnowledgeBaseParsedVectorDB,
    context: SessionContext,
  ): Promise<void>
  update(
    id: string,
    item: KnowledgeBaseParsedVectorDB,
    context: SessionContext,
  ): Promise<void>
  delete(id: string, context: SessionContext): Promise<void>
  search(
    query: string,
    topK: number,
    context: SessionContext,
  ): Promise<KnowledgeBaseParsedVectorDB[]>
  searchSimilar(
    query: string,
    topK: number,
    context: SessionContext,
  ): Promise<KnowledgeBaseParsedVectorDB[]>
  searchByKeywords(
    keywords: string[],
    topK: number,
    context: SessionContext,
  ): Promise<KnowledgeBaseParsedVectorDB[]>
}

export class PineconeKnowledgeBaseParsedVectorDBRepository
  implements KnowledgeBaseParsedVectorDBRepository
{
  private pinecone: Pinecone

  constructor(pineconeApiKey: string) {
    this.pinecone = new Pinecone({ apiKey: pineconeApiKey })
  }

  private getNamespace(context: SessionContext): Index<RecordMetadata> {
    const namespace = __getNamespace(context)
    return this.pinecone.index("knowledge-base-parsed").namespace(namespace)
  }

  async create(
    item: KnowledgeBaseParsedVectorDB,
    context: SessionContext,
  ): Promise<void> {
    const namespace = this.getNamespace(context)
    await namespace.upsertRecords([
      {
        _id: item.id,
        text: item.content,
        title: item.title,
        keywords: item.keywords,
        // category: item.category,
        // tags: item.tags,
      },
    ])
  }

  async update(
    id: string,
    item: KnowledgeBaseParsedVectorDB,
    context: SessionContext,
  ): Promise<void> {
    const namespace = this.getNamespace(context)
    await namespace.upsertRecords([
      {
        _id: id,
        text: item.content,
        title: item.title,
        keywords: item.keywords,
        // category: item.category,
        // tags: item.tags,
      },
    ])
  }

  async delete(id: string, context: SessionContext): Promise<void> {
    const namespace = this.getNamespace(context)
    await namespace.deleteMany([id])
  }

  async search(
    text: string,
    topK: number = 5,
    context: SessionContext,
  ): Promise<KnowledgeBaseParsedVectorDB[]> {
    const namespace = this.getNamespace(context)
    const searchResult = await namespace.searchRecords({
      query: {
        topK,
        inputs: { text: text }, // this gets embedded automatically
      },
    })

    return (searchResult.result.hits || []).map((hit: any) => {
      const metadata = hit.fields as any
      return {
        id: hit._id,
        title: metadata?.title || "",
        content: metadata?.text || "",
        keywords: metadata?.keywords || [],
        category: metadata?.category || undefined,
      }
    })
  }

  async searchSimilar(
    query: string,
    topK: number = 5,
    context: SessionContext,
  ): Promise<KnowledgeBaseParsedVectorDB[]> {
    return this.search(query, topK, context)
  }

  async searchByKeywords(
    keywords: string[],
    topK: number = 5,
    context: SessionContext,
  ): Promise<KnowledgeBaseParsedVectorDB[]> {
    const keywordQuery = keywords.join(" ")
    return this.search(keywordQuery, topK, context)
  }
}
