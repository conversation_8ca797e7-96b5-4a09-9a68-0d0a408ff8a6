import { Pinecone, Index, RecordMetadata } from "@pinecone-database/pinecone"
import { SessionContext } from "../auth/types"
import { __getNamespace } from "../BaseVectorDBRepository"

export interface MessageTemplateVectorDB {
  id: string
  query: string
  template: string
}

export interface MessageTemplateVectorDBRepository {
  create(item: MessageTemplateVectorDB, context: SessionContext): Promise<void>
  update(
    id: string,
    item: MessageTemplateVectorDB,
    context: SessionContext,
  ): Promise<void>
  delete(id: string, context: SessionContext): Promise<void>
  search(
    query: string,
    topK: number,
    context: SessionContext,
  ): Promise<MessageTemplateVectorDB[]>
}

export class PineconeMessageTemplateVectorDBRepository
  implements MessageTemplateVectorDBRepository
{
  private pinecone: Pinecone

  constructor(pineconeApiKey: string) {
    this.pinecone = new Pinecone({ apiKey: pineconeApiKey })
  }

  private getNamespace(context: SessionContext): Index<RecordMetadata> {
    const namespace = __getNamespace(context)
    return this.pinecone.index("message-template").namespace(namespace)
  }

  async create(
    item: MessageTemplateVectorDB,
    context: SessionContext,
  ): Promise<void> {
    const namespace = this.getNamespace(context)
    await namespace.upsertRecords([
      {
        _id: item.id,
        text: item.query,
        template: item.template,
      },
    ])
  }

  async update(
    id: string,
    item: MessageTemplateVectorDB,
    context: SessionContext,
  ): Promise<void> {
    const namespace = this.getNamespace(context)
    await namespace.upsertRecords([
      {
        _id: id,
        text: item.query,
        template: item.template,
      },
    ])
  }

  async delete(id: string, context: SessionContext): Promise<void> {
    const namespace = this.getNamespace(context)
    await namespace.deleteMany([id])
  }

  async search(
    text: string,
    topK: number = 3,
    context: SessionContext,
  ): Promise<MessageTemplateVectorDB[]> {
    const namespace = this.getNamespace(context)
    const searchResult = await namespace.searchRecords({
      query: {
        topK,
        inputs: { text: text }, // this gets embedded automatically
      },
    })

    return (searchResult.result.hits || []).map((hit: any) => {
      const metadata = hit.fields as any

      return {
        id: hit._id,
        query: metadata?.text || "",
        template: metadata?.template || "",
      }
    })
  }
}
