import {
  OrganizationCollaborator,
  OrganizationCollaboratorCreateInput,
  OrganizationCollaboratorUpdateInput,
  OrganizationCollaboratorQueryParams,
  OrganizationCollaboratorBusinessLogicInterface,
  InvitationLink,
  InvitationInfo,
} from "./interface"
import { createError } from "@/lib/utils/common"
import { OrganizationCollaboratorDBRepository } from "./DBRepository"
import { SessionContext } from "../auth/types"
import { nanoid } from "nanoid"

export class OrganizationCollaboratorBusinessLogic
  implements OrganizationCollaboratorBusinessLogicInterface
{
  constructor(private readonly db: OrganizationCollaboratorDBRepository) {}

  private trimCreateInput(data: OrganizationCollaboratorCreateInput): OrganizationCollaboratorCreateInput {
    return {
      ...data,
      email: data.email.trim().toLowerCase(),
    }
  }

  private trimUpdateInput(data: OrganizationCollaboratorUpdateInput): OrganizationCollaboratorUpdateInput {
    return {
      ...data,
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters: { field: string; value: any }[] = []
    
    // Users can only see collaborators from their organization
    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    }
    
    return filters
  }

  async getAll(
    params: OrganizationCollaboratorQueryParams,
    context: SessionContext,
  ): Promise<{ items: OrganizationCollaborator[]; total: number }> {
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      ...params,
      filters: [...(params.filters || []), ...contextFilters],
    }

    return this.db.getAll(paramsWithContext)
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<OrganizationCollaborator | null> {
    if (!id || !id.trim()) {
      throw createError("Collaborator ID is required", "INVALID_ID")
    }

    const collaborator = await this.db.getById(id, includeDeleted)
    
    // Check if user has access to this collaborator (same organization)
    if (collaborator && collaborator.organizationId !== context.organization?.id) {
      return null
    }

    return collaborator
  }

  async getByToken(
    token: string,
    context: SessionContext,
  ): Promise<OrganizationCollaborator | null> {
    if (!token || !token.trim()) {
      throw createError("Token is required", "INVALID_TOKEN")
    }

    return this.db.getByToken(token)
  }

  async create(
    data: OrganizationCollaboratorCreateInput,
    context: SessionContext,
  ): Promise<OrganizationCollaborator> {
    const trimmedData = this.trimCreateInput(data)

    // Ensure organization ID matches context
    if (!context.organization?.id) {
      throw createError("Organization context is required", "MISSING_ORGANIZATION")
    }

    // Check for existing collaborator with same email in organization
    const existing = await this.db.getByEmail(trimmedData.email, context.organization.id)
    if (existing) {
      throw createError(
        "User is already a collaborator in this organization",
        "DUPLICATE_COLLABORATOR",
      )
    }

    const dataWithContext = {
      ...trimmedData,
      organizationId: context.organization.id,
      invitedBy: context.user.id,
    }

    return this.db.create(dataWithContext)
  }

  async update(
    id: string,
    data: OrganizationCollaboratorUpdateInput,
    context: SessionContext,
  ): Promise<OrganizationCollaborator | null> {
    const existingCollaborator = await this.getById(id, context)
    if (!existingCollaborator) {
      return null
    }

    const trimmedData = this.trimUpdateInput(data)

    const dataWithContext = {
      ...trimmedData,
      updatedBy: context.user.id,
    }

    return this.db.update(id, dataWithContext)
  }

  async delete(id: string, context: SessionContext): Promise<boolean> {
    const existingCollaborator = await this.getById(id, context)
    if (!existingCollaborator) {
      return false
    }

    return this.db.delete(id)
  }

  async inviteByEmail(
    email: string,
    role: string,
    context: SessionContext,
  ): Promise<{ success: boolean }> {
    if (!email || !email.trim()) {
      throw createError("Email is required", "INVALID_EMAIL")
    }

    if (!["admin", "member", "editor"].includes(role)) {
      throw createError("Invalid role", "INVALID_ROLE")
    }

    const token = nanoid(32)
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

    await this.create({
      email: email.trim().toLowerCase(),
      role: role as "admin" | "member" | "editor",
      status: "invited",
      invitedBy: context.user.id,
      organizationId: context.organization!.id,
      invitationToken: token,
      tokenExpiresAt: expiresAt,
    }, context)

    // TODO: Send invitation email here
    
    return { success: true }
  }

  async generateInvitationLink(
    role: string,
    context: SessionContext,
  ): Promise<InvitationLink> {
    if (!["admin", "member", "editor"].includes(role)) {
      throw createError("Invalid role", "INVALID_ROLE")
    }

    const token = nanoid(32)
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

    // Create a temporary invitation record
    await this.create({
      email: "", // Will be filled when user accepts
      role: role as "admin" | "member" | "editor",
      status: "invited",
      invitedBy: context.user.id,
      organizationId: context.organization!.id,
      invitationToken: token,
      tokenExpiresAt: expiresAt,
    }, context)

    // TODO: Get base URL from environment or request
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
    const invitationLink = `${baseUrl}/invitations/accept?token=${token}`

    return {
      invitationLink,
      token,
      expiresAt: expiresAt.toISOString(),
    }
  }

  async acceptInvitation(
    token: string,
    context: SessionContext,
  ): Promise<{ success: boolean }> {
    const invitation = await this.getByToken(token, context)
    if (!invitation) {
      throw createError("Invalid or expired invitation", "INVALID_INVITATION")
    }

    if (invitation.tokenExpiresAt && invitation.tokenExpiresAt < new Date()) {
      throw createError("Invitation has expired", "EXPIRED_INVITATION")
    }

    await this.update(invitation.id, {
      status: "active",
      acceptedAt: new Date(),
      userId: context.user.id,
      email: invitation.email || context.user.email,
    }, context)

    return { success: true }
  }

  async declineInvitation(
    token: string,
    context: SessionContext,
  ): Promise<{ success: boolean }> {
    const invitation = await this.getByToken(token, context)
    if (!invitation) {
      throw createError("Invalid invitation", "INVALID_INVITATION")
    }

    await this.update(invitation.id, {
      status: "declined",
      declinedAt: new Date(),
    }, context)

    return { success: true }
  }

  async getInvitationInfo(
    token: string,
    context: SessionContext,
  ): Promise<InvitationInfo> {
    const invitation = await this.getByToken(token, context)
    if (!invitation) {
      throw createError("Invalid invitation", "INVALID_INVITATION")
    }

    // TODO: Get actual inviter name and organization name from database
    return {
      inviterName: "Jane Doe", // Mock data
      organizationName: "Acme Corp", // Mock data
      role: invitation.role,
    }
  }
}
