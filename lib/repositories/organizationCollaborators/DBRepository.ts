import { BaseDbRepository } from "../BaseDBRepository"
import {
  OrganizationCollaborator,
  OrganizationCollaboratorCreateInput,
  OrganizationCollaboratorUpdateInput,
  OrganizationCollaboratorQueryParams,
} from "./interface"

export type OrganizationCollaboratorDbQueryParams = OrganizationCollaboratorQueryParams

export interface OrganizationCollaboratorDBRepository
  extends BaseDbRepository<
    OrganizationCollaborator,
    OrganizationCollaboratorCreateInput,
    OrganizationCollaboratorUpdateInput,
    OrganizationCollaboratorDbQueryParams
  > {
  getByToken(token: string, includeDeleted?: boolean): Promise<OrganizationCollaborator | null>
  getByEmail(email: string, organizationId: string, includeDeleted?: boolean): Promise<OrganizationCollaborator | null>
}
