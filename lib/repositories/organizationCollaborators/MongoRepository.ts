import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import { Document, WithId } from "mongodb"
import { MongoDriver } from "../MongoDriver"
import { buildMongoQuery } from "../queryBuilder"
import { OrganizationCollaboratorDbQueryParams, OrganizationCollaboratorDBRepository } from "./DBRepository"
import { OrganizationCollaborator, OrganizationCollaboratorCreateInput, OrganizationCollaboratorUpdateInput } from "./interface"
import { nanoid } from "nanoid"

function mapMongoDocToOrganizationCollaborator(doc: WithId<Document> | null): OrganizationCollaborator | null {
  if (!doc) return null
  const { _id, ...rest } = doc
  return {
    ...rest,
  } as unknown as OrganizationCollaborator
}

export class MongoOrganizationCollaboratorRepository implements OrganizationCollaboratorDBRepository {
  private collection

  constructor(driver: MongoDriver) {
    this.collection = driver.getCollection(MONGO_COLLECTIONS.ORGANIZATION_COLLABORATORS)
  }

  async getById(id: string, includeDeleted = false): Promise<OrganizationCollaborator | null> {
    const query: any = { id }
    if (!includeDeleted) query.deletedAt = { $exists: false }

    const doc = await this.collection.findOne(query)
    return mapMongoDocToOrganizationCollaborator(doc)
  }

  async getByToken(token: string, includeDeleted = false): Promise<OrganizationCollaborator | null> {
    const query: any = { invitationToken: token }
    if (!includeDeleted) query.deletedAt = { $exists: false }

    const doc = await this.collection.findOne(query)
    return mapMongoDocToOrganizationCollaborator(doc)
  }

  async getByEmail(email: string, organizationId: string, includeDeleted = false): Promise<OrganizationCollaborator | null> {
    const query: any = { email, organizationId }
    if (!includeDeleted) query.deletedAt = { $exists: false }

    const doc = await this.collection.findOne(query)
    return mapMongoDocToOrganizationCollaborator(doc)
  }

  async getAll(
    params: OrganizationCollaboratorDbQueryParams,
  ): Promise<{ items: OrganizationCollaborator[]; total: number }> {
    const { query, sort, limit, offset } = buildMongoQuery(
      {},
      {
        ...params,
        offset: params?.offset,
      },
      ["email"], // searchable fields
    )

    const cursor = this.collection
      .find(query)
      .sort(sort)
      .skip(offset)
      .limit(limit)

    const docs = await cursor.toArray()
    const items = docs
      .map(mapMongoDocToOrganizationCollaborator)
      .filter((i): i is OrganizationCollaborator => i !== null)

    const total = await this.collection.countDocuments(query)

    return { items, total }
  }

  async getCount(params: OrganizationCollaboratorDbQueryParams): Promise<{ total: number }> {
    const { query } = buildMongoQuery(
      {},
      params,
      ["email"], // searchable fields
    )

    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async create(data: OrganizationCollaboratorCreateInput): Promise<OrganizationCollaborator> {
    const now = new Date()
    const doc = {
      id: nanoid(),
      ...data,
      createdAt: now,
      updatedAt: now,
    }

    await this.collection.insertOne(doc)
    return doc as OrganizationCollaborator
  }

  async update(id: string, data: OrganizationCollaboratorUpdateInput): Promise<OrganizationCollaborator | null> {
    const updateDoc = {
      ...data,
      updatedAt: new Date(),
    }

    const result = await this.collection.findOneAndUpdate(
      { id },
      { $set: updateDoc },
      { returnDocument: "after" }
    )

    return mapMongoDocToOrganizationCollaborator(result)
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ id })
      return result.deletedCount === 1
    } else {
      const result = await this.collection.updateOne(
        { id },
        { $set: { deletedAt: new Date() } }
      )
      return result.modifiedCount === 1
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { id },
      { $unset: { deletedAt: "" } }
    )
    return result.modifiedCount === 1
  }

  async clear(): Promise<void> {
    await this.collection.deleteMany({})
  }
}
