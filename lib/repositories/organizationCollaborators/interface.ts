import { SessionContext } from "../auth/types"
import { BaseQueryParams } from "../BaseDBRepository"

export interface OrganizationCollaborator {
  id: string
  organizationId: string
  userId: string
  email: string
  role: "admin" | "member" | "editor"
  status: "invited" | "active" | "declined"
  invitedAt: Date
  acceptedAt?: Date
  declinedAt?: Date
  invitedBy: string
  invitationToken?: string
  tokenExpiresAt?: Date
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
}

export interface OrganizationCollaboratorCreateInput {
  organizationId: string
  userId?: string
  email: string
  role: "admin" | "member" | "editor"
  status: "invited" | "active"
  invitedBy: string
  invitationToken?: string
  tokenExpiresAt?: Date
}

export interface OrganizationCollaboratorUpdateInput {
  role?: "admin" | "member" | "editor"
  status?: "invited" | "active" | "declined"
  acceptedAt?: Date
  declinedAt?: Date
  updatedBy?: string
}

export interface OrganizationCollaboratorQueryParams extends BaseQueryParams<OrganizationCollaborator> {
  organizationId?: string
  userId?: string
  email?: string
  role?: string
  status?: string
  search?: string
  filters?: { field: keyof OrganizationCollaborator | string; value: any }[]
  sort?: { field: keyof OrganizationCollaborator | string; direction: "ASC" | "DESC" }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface InvitationLink {
  invitationLink: string
  token: string
  expiresAt: string
}

export interface InvitationInfo {
  inviterName: string
  organizationName: string
  role: string
}

export interface OrganizationCollaboratorBusinessLogicInterface {
  getAll(
    params: OrganizationCollaboratorQueryParams,
    context: SessionContext,
  ): Promise<{ items: OrganizationCollaborator[]; total: number }>
  
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<OrganizationCollaborator | null>
  
  getByToken(
    token: string,
    context: SessionContext,
  ): Promise<OrganizationCollaborator | null>
  
  create(
    data: OrganizationCollaboratorCreateInput,
    context: SessionContext,
  ): Promise<OrganizationCollaborator>
  
  update(
    id: string,
    data: OrganizationCollaboratorUpdateInput,
    context: SessionContext,
  ): Promise<OrganizationCollaborator | null>
  
  delete(id: string, context: SessionContext): Promise<boolean>
  
  inviteByEmail(
    email: string,
    role: string,
    context: SessionContext,
  ): Promise<{ success: boolean }>
  
  generateInvitationLink(
    role: string,
    context: SessionContext,
  ): Promise<InvitationLink>
  
  acceptInvitation(
    token: string,
    context: SessionContext,
  ): Promise<{ success: boolean }>
  
  declineInvitation(
    token: string,
    context: SessionContext,
  ): Promise<{ success: boolean }>
  
  getInvitationInfo(
    token: string,
    context: SessionContext,
  ): Promise<InvitationInfo>
}
