import {
  Organization,
  OrganizationCreateInput,
  OrganizationUpdateInput,
  OrganizationQueryParams,
  OrganizationBusinessLogicInterface,
} from "./interface"
import { OrganizationDBRepository } from "./DBRepository"
import { SessionContext } from "../auth/types"
import { createError } from "@/lib/utils/common"

export class OrganizationBusinessLogic implements OrganizationBusinessLogicInterface {
  constructor(private db: OrganizationDBRepository) {}

  private trimCreateInput(data: OrganizationCreateInput): OrganizationCreateInput {
    return {
      ...data,
      name: data.name.trim(),
    }
  }

  private trimUpdateInput(data: OrganizationUpdateInput): OrganizationUpdateInput {
    return {
      ...data,
      name: data.name?.trim(),
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters: { field: string; value: any }[] = []
    
    // Users can only see organizations they created
    // In a multi-tenant system, you might want to add organization-level filtering here
    filters.push({ field: "createdBy", value: context.user.id })
    
    return filters
  }

  async getAll(
    params: OrganizationQueryParams,
    context: SessionContext,
  ): Promise<{ items: Organization[]; total: number }> {
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      ...params,
      filters: [...(params.filters || []), ...contextFilters],
    }
    return this.db.getAll(paramsWithContext)
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<Organization | null> {
    if (!id || !id.trim()) {
      throw createError("Organization ID is required", "INVALID_ID")
    }

    const organization = await this.db.getById(id, includeDeleted)
    
    // Check if user has access to this organization
    if (organization && organization.createdBy !== context.user.id) {
      return null
    }

    return organization
  }

  async create(
    data: OrganizationCreateInput,
    context: SessionContext,
  ): Promise<Organization> {
    const trimmedData = this.trimCreateInput(data)

    // Check for duplicate name for this user
    const existing = await this.db.getAll({
      filters: [
        { field: "name", value: trimmedData.name },
        { field: "createdBy", value: context.user.id },
      ],
    })
    if (existing.items.length > 0) {
      throw createError(
        "Organization with the same name already exists",
        "DUPLICATE_NAME",
      )
    }

    const dataWithContext = {
      ...trimmedData,
      createdBy: context.user.id,
    }

    return this.db.create(dataWithContext)
  }

  async update(
    id: string,
    data: OrganizationUpdateInput,
    context: SessionContext,
  ): Promise<Organization | null> {
    const existingOrganization = await this.getById(id, context)
    if (!existingOrganization) {
      return null
    }

    const trimmedData = this.trimUpdateInput(data)

    // Check for duplicate name if name is being updated
    if (trimmedData.name && trimmedData.name !== existingOrganization.name) {
      const existing = await this.db.getAll({
        filters: [
          { field: "name", value: trimmedData.name },
          { field: "createdBy", value: context.user.id },
        ],
      })
      if (existing.items.length > 0) {
        throw createError(
          "Organization with the same name already exists",
          "DUPLICATE_NAME",
        )
      }
    }

    const dataWithContext = {
      ...trimmedData,
      updatedBy: context.user.id,
    }

    return this.db.update(id, dataWithContext)
  }

  async delete(id: string, context: SessionContext): Promise<boolean> {
    const existingOrganization = await this.getById(id, context)
    if (!existingOrganization) {
      return false
    }

    return this.db.delete(id)
  }
}
