import { Organization, OrganizationCreateInput, OrganizationUpdateInput } from "./interface"
import { BaseDbRepository, BaseQueryParams } from "../BaseDBRepository"

export type OrganizationDbQueryParams = BaseQueryParams<Organization>

export interface OrganizationDBRepository
  extends BaseDbRepository<
    Organization,
    OrganizationCreateInput,
    OrganizationUpdateInput,
    OrganizationDbQueryParams
  > {}
