import {
  Organization,
  OrganizationCreateInput,
  OrganizationUpdateInput,
  OrganizationQueryParams,
} from "./interface"
import { WithId, Document } from "mongodb"
import { MongoDriver } from "../MongoDriver"
import { OrganizationDBRepository } from "./DBRepository"
import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import { buildMongoQuery } from "../queryBuilder"
import { nanoid } from "nanoid"

function generateId(): string {
  return `org-${nanoid(10)}`
}

function mapMongoDocToOrganization(
  doc: WithId<Document> | null,
): Organization | null {
  if (!doc) return null
  const { _id, ...rest } = doc
  return {
    ...rest,
  } as unknown as Organization
}

export class MongoOrganizationRepository implements OrganizationDBRepository {
  private collection

  constructor(driver: MongoDriver) {
    this.collection = driver.getCollection(MONGO_COLLECTIONS.ORGANIZATIONS)
    this.ensureIndexes()
  }

  private async ensureIndexes() {
    await this.collection.createIndex({ id: 1 }, { unique: true })
    await this.collection.createIndex({ name: 1 })
    await this.collection.createIndex({ createdBy: 1 })
    await this.collection.createIndex({ isActive: 1 })
    await this.collection.createIndex({ createdAt: -1 })
  }

  async getById(
    id: string,
    includeDeleted = false,
  ): Promise<Organization | null> {
    const query: any = { id }
    if (!includeDeleted) query.deletedAt = { $exists: false }

    const doc = await this.collection.findOne(query)
    return mapMongoDocToOrganization(doc)
  }

  async getAll(
    params: OrganizationQueryParams,
  ): Promise<{ items: Organization[]; total: number }> {
    const { query, sort, limit, offset } = buildMongoQuery(
      { query: {}, sort: {} },
      params,
      ["name"],
    )

    const cursor = this.collection
      .find(query)
      .sort(sort)
      .skip(offset)
      .limit(limit)

    const docs = await cursor.toArray()
    const items = docs
      .map(mapMongoDocToOrganization)
      .filter((i): i is Organization => i !== null)

    const total = await this.collection.countDocuments(query)
    return { items, total }
  }

  async create(
    data: OrganizationCreateInput & { createdBy: string },
  ): Promise<Organization> {
    const now = new Date()
    const doc = {
      ...data,
      id: generateId(),
      createdAt: now,
      updatedAt: now,
    }
    await this.collection.insertOne(doc)
    return mapMongoDocToOrganization(doc)!
  }

  async update(
    id: string,
    data: OrganizationUpdateInput,
  ): Promise<Organization | null> {
    const result = await this.collection.findOneAndUpdate(
      { id, deletedAt: { $exists: false } },
      { $set: { ...data, updatedAt: new Date() } },
      { returnDocument: "after" },
    )
    return result ? mapMongoDocToOrganization(result) : null
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ id })
      return result.deletedCount > 0
    } else {
      const result = await this.collection.updateOne(
        { id, deletedAt: { $exists: false } },
        { $set: { deletedAt: new Date(), updatedAt: new Date() } },
      )
      return result.modifiedCount > 0
    }
  }

  async getCount(params: OrganizationQueryParams): Promise<{ total: number }> {
    const { query } = buildMongoQuery({ query: {}, sort: {} }, params, ["name"])
    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { id, deletedAt: { $exists: true } },
      { $unset: { deletedAt: "" }, $set: { updatedAt: new Date() } },
    )
    return result.modifiedCount > 0
  }

  async clear(): Promise<void> {
    await this.collection.deleteMany({})
  }
}
