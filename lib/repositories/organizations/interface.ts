import { SessionContext } from "../auth/types"
import { BaseQueryParams } from "../BaseDBRepository"

export interface Organization {
  id: string
  name: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
}

export interface OrganizationCreateInput {
  name: string
  isActive?: boolean
  createdBy: string
}

export interface OrganizationUpdateInput {
  name?: string
  isActive?: boolean
  updatedBy?: string
}

export interface OrganizationQueryParams extends BaseQueryParams<Organization> {
  search?: string
  filters?: { field: keyof Organization | string; value: any }[]
  sort?: { field: keyof Organization | string; direction: "ASC" | "DESC" }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface OrganizationBusinessLogicInterface {
  getAll(
    params: OrganizationQueryParams,
    context: SessionContext,
  ): Promise<{ items: Organization[]; total: number }>
  
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<Organization | null>
  
  create(
    data: OrganizationCreateInput,
    context: SessionContext,
  ): Promise<Organization>
  
  update(
    id: string,
    data: OrganizationUpdateInput,
    context: SessionContext,
  ): Promise<Organization | null>
  
  delete(id: string, context: SessionContext): Promise<boolean>
}
