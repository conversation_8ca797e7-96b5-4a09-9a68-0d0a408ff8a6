import {
  TestConversation,
  TestConversationCreateInput,
  TestConversationUpdateInput,
  TestConversationQueryParams,
  TestConversationBusinessLogicInterface,
} from "./interface"
import { createError, generateId } from "@/lib/utils/common"
import { TestConversationDBRepository } from "./DBRepository"
import { SessionContext } from "../auth/types"

function mapDocToTestConversation(doc: any): TestConversation | null {
  if (!doc) return null
  const { _id, ...rest } = doc
  return {
    ...rest,
  } as TestConversation
}

export class TestConversationBusinessLogic
  implements TestConversationBusinessLogicInterface
{
  constructor(private readonly db: TestConversationDBRepository) {}

  private validateId(id: string) {
    if (!id || !id.trim())
      throw createError("TestConversation ID is required", "INVALID_ID")
  }

  private trimCreateInput(
    data: TestConversationCreateInput,
  ): TestConversationCreateInput {
    return {
      ...data,
      customerName: data.customerName.trim(),
      customerPhone: data.customerPhone?.trim() ?? "",
      scenario: data.scenario,
      messages: data.messages,
      status: data.status ?? "OPEN", // Default to OPEN if not specified
      isAiEnabled: data.isAiEnabled ?? true,
      scenarioDescription: data.scenarioDescription,
      sampleMessages: data.sampleMessages,
      title: data.title,
    }
  }

  private trimUpdateInput(
    data: TestConversationUpdateInput,
  ): TestConversationUpdateInput {
    return {
      ...data,
      customerName: data.customerName?.trim(),
      customerPhone: data.customerPhone?.trim(),
      scenario: data.scenario,
      messages: data.messages,
      status: data.status,
      isAiEnabled: data.isAiEnabled,
      scenarioDescription: data.scenarioDescription,
      sampleMessages: data.sampleMessages,
      title: data.title,
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []
    if (context.user.id) {
      filters.push({ field: "createdBy", value: context.user.id })
    }
    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    }
    return filters
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<TestConversation | null> {
    this.validateId(id)
    const contextFilters = this.buildContextFilters(context)
    const result = await this.db.getById(id, includeDeleted)

    return mapDocToTestConversation(result)
  }

  async getAll(
    params: TestConversationQueryParams,
    context: SessionContext,
  ): Promise<{ items: TestConversation[]; total: number }> {
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      ...params,
      offset: params?.page,
      filters: [...(params.filters || []), ...contextFilters],
    }

    const { items, total } = await this.db.getAll(paramsWithContext)
    return {
      items: items
        .map(mapDocToTestConversation)
        .filter((i): i is TestConversation => i !== null),
      total,
    }
  }

  async create(
    data: TestConversationCreateInput,
    context: SessionContext,
  ): Promise<TestConversation> {
    const trimmedData = this.trimCreateInput(data)
    const dataWithContext = {
      ...trimmedData,
      id: generateId("testConversationroom"),
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    const created = await this.db.create(dataWithContext)
    return mapDocToTestConversation(created)!
  }

  async update(
    id: string,
    data: TestConversationUpdateInput,
    context: SessionContext,
  ): Promise<TestConversation | null> {
    this.validateId(id)

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA")
    }

    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("TestConversation not found", "NOT_FOUND")
    }

    const trimmedData = {
      ...this.trimUpdateInput(data),
      updatedBy: context.user.id,
    }

    const updated = await this.db.update(id, trimmedData)
    return updated ? mapDocToTestConversation(updated) : null
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)
    const contextFilters = this.buildContextFilters(context)

    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("TestConversation not found", "NOT_FOUND")
    }

    return this.db.delete(id, hardDelete)
  }

  async restore(id: string, context: SessionContext): Promise<boolean> {
    this.validateId(id)
    const contextFilters = this.buildContextFilters(context)
    throw new Error("Not implemented")
  }
}
