import { SessionContext } from "@/lib/repositories/auth/types"

export interface User {
  id: string
  email: string
  name: string
  tags: string[]
  isActive: boolean
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
}

export interface UserCreateInput {
  email: string
  name: string
  tags: string[]
  isActive?: boolean
}

export interface UserUpdateInput {
  email?: string
  name?: string
  tags: string[]
  isActive?: boolean
}

export interface UserQueryParams {
  search?: string
  filters?: { field: keyof User | string; value: any }[]
  sort?: { field: keyof User | string; direction: "ASC" | "DESC" }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface UserBusinessLogicInterface {
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<User | null>
  getAll(
    params: UserQueryParams,
    context: SessionContext,
  ): Promise<{
    items: User[]
    total: number
  }>
  create(data: UserCreateInput, context: SessionContext): Promise<User>
  update(
    id: string,
    data: UserUpdateInput,
    context: SessionContext,
  ): Promise<User | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>
  restore(id: string, context: SessionContext): Promise<boolean>
  bulkCreate(data: UserCreateInput[], context: SessionContext): Promise<User[]>
  bulkUpdate(
    updates: { id: string; data: UserUpdateInput }[],
    context: SessionContext,
  ): Promise<number>
  bulkDelete(
    ids: string[],
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<number>
}
