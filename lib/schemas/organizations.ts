import { z } from "zod"

export const OrganizationCreateSchema = z.object({
  name: z.string().min(1, "Organization name is required").max(100, "Organization name must be less than 100 characters"),
  isActive: z.boolean().optional().default(true),
})

export const OrganizationUpdateSchema = z.object({
  name: z.string().min(1, "Organization name is required").max(100, "Organization name must be less than 100 characters").optional(),
  isActive: z.boolean().optional(),
})

export const OrganizationIdSchema = z.object({
  id: z.string().min(1, "Organization ID is required"),
})

export type OrganizationCreateInput = z.infer<typeof OrganizationCreateSchema>
export type OrganizationUpdateInput = z.infer<typeof OrganizationUpdateSchema>
export type OrganizationIdInput = z.infer<typeof OrganizationIdSchema>
