import { BaseAPI } from "./baseApi"
import { PaginatedResponse, PagingAndSearch } from "./types"

export interface Collaborator {
  id: string
  email: string
  role: string
  status: "invited" | "active" | "declined"
  invitedAt?: string
  acceptedAt?: string
}

export interface InvitePayload {
  email: string
  role?: string
}

export interface UpdateRolePayload {
  collaboratorId: string
  role: string
}

export class CollaborationAPI extends BaseAPI {
  static All(params?: PagingAndSearch<{}>) {
    const queryString = params
      ? `?${new URLSearchParams(PagingAndSearch.toRecordString(params)).toString()}`
      : ""

    return new BaseAPI(
      `/organization/collaborators${queryString}`,
      undefined,
      "GET",
      true,
      {},
      {
        items: [
          {
            id: "user-1",
            email: "<EMAIL>",
            role: "admin",
            status: "active",
            acceptedAt: new Date().toISOString(),
          },
          {
            id: "user-2",
            email: "<EMAIL>",
            role: "member",
            status: "invited",
            invitedAt: new Date().toISOString(),
          },
        ],
        total: 2,
        page: 1,
        pageSize: 10,
      },
    ).build<PaginatedResponse<Collaborator>>()
  }

  static GetCollaboratorById(collaboratorId: string) {
    return new BaseAPI(
      `/organization/collaborators/${collaboratorId}`,
      undefined,
      "GET",
      true,
      {},
      {
        id: collaboratorId,
        email: "<EMAIL>",
        role: "admin",
        status: "active",
        invitedAt: "2023-07-20T12:00:00Z",
        acceptedAt: "2023-07-22T15:00:00Z",
      },
    ).build<Collaborator>()
  }

  static InviteCollaborator(payload: InvitePayload) {
    return new BaseAPI(
      `/organization/invite`,
      payload,
      "POST",
      true,
      {},
      { success: true },
    ).build<{ success: boolean }>()
  }

  static RemoveCollaborator(collaboratorId: string) {
    return new BaseAPI(
      `/organization/collaborators/${collaboratorId}`,
      undefined,
      "DELETE",
      true,
      {},
      { success: true },
    ).build<{ success: boolean }>()
  }

  static UpdateCollaboratorRole(payload: UpdateRolePayload) {
    return new BaseAPI(
      `/organization/collaborators/${payload.collaboratorId}`,
      { role: payload.role },
      "PUT",
      true,
      {},
      { success: true },
    ).build<{ success: boolean }>()
  }

  static AcceptInvitation(token: string) {
    return new BaseAPI(
      `/organization/invitations/accept`,
      { token },
      "POST",
      true,
      {},
      { success: true },
    ).build<{ success: boolean }>()
  }

  static DeclineInvitation(token: string) {
    return new BaseAPI(
      `/organization/invitations/decline`,
      { token },
      "POST",
      true,
      {},
      { success: true },
    ).build<{ success: boolean }>()
  }

  static GetInvitationInfo(token: string) {
    return new BaseAPI(
      `/organization/invitations/${token}`,
      undefined,
      "GET",
      true,
      {},
      {
        inviterName: "Jane Doe",
        organizationName: "Acme Corp",
        role: "editor",
      },
    ).build<{
      inviterName: string
      organizationName: string
      role: string
    }>()
  }

  static GenerateInvitationLink(payload?: { role?: string }) {
    return new BaseAPI(
      `/organization/invitation-link`,
      payload || {},
      "POST",
      true,
      {},
      {
        invitationLink: `${typeof window !== "undefined" ? window.location.origin : "https://example.com"}/invitations/accept?token=abc123def456`,
        token: "abc123def456",
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      },
    ).build<{
      invitationLink: string
      token: string
      expiresAt: string
    }>()
  }
}
