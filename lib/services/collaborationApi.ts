import { BaseAPI } from "./baseApi"
import { PaginatedResponse, PagingAndSearch } from "./types"

export interface Collaborator {
  id: string
  email: string
  role: string
  status: "invited" | "active" | "declined"
  invitedAt?: string
  acceptedAt?: string
}

export interface InvitePayload {
  email: string
  role?: string
}

export interface UpdateRolePayload {
  collaboratorId: string
  role: string
}

export class CollaborationAPI extends BaseAPI {
  static All(params?: PagingAndSearch<{}>) {
    const queryString = params
      ? `?${new URLSearchParams(PagingAndSearch.toRecordString(params)).toString()}`
      : ""

    return new BaseAPI(
      `/organization/collaborators${queryString}`,
      undefined,
      "GET",
    ).build<PaginatedResponse<Collaborator>>()
  }

  static GetCollaboratorById(collaboratorId: string) {
    return new BaseAPI(
      `/organization/collaborators/${collaboratorId}`,
      undefined,
      "GET",
    ).build<Collaborator>()
  }

  static InviteCollaborator(payload: InvitePayload) {
    return new BaseAPI(`/organization/invite`, payload, "POST").build<{
      success: boolean
    }>()
  }

  static RemoveCollaborator(collaboratorId: string) {
    return new BaseAPI(
      `/organization/collaborators/${collaboratorId}`,
      undefined,
      "DELETE",
    ).build<{ success: boolean }>()
  }

  static UpdateCollaboratorRole(payload: UpdateRolePayload) {
    return new BaseAPI(
      `/organization/collaborators/${payload.collaboratorId}`,
      { role: payload.role },
      "PUT",
    ).build<{ success: boolean }>()
  }

  static AcceptInvitation(token: string) {
    return new BaseAPI(
      `/organization/invitations/accept`,
      { token },
      "POST",
    ).build<{ success: boolean }>()
  }

  static DeclineInvitation(token: string) {
    return new BaseAPI(
      `/organization/invitations/decline`,
      { token },
      "POST",
    ).build<{ success: boolean }>()
  }

  static GetInvitationInfo(token: string) {
    return new BaseAPI(
      `/organization/invitations/${token}`,
      undefined,
      "GET",
    ).build<{
      inviterName: string
      organizationName: string
      role: string
    }>()
  }

  static GenerateInvitationLink(payload?: { role?: string }) {
    return new BaseAPI(
      `/organization/invitation-link`,
      payload || {},
      "POST",
    ).build<{
      invitationLink: string
      token: string
      expiresAt: string
    }>()
  }
}
