import { BaseAPI } from "./baseApi"
import { Organization } from "@/lib/repositories/organizations/interface"
import { PagingAndSearch, PaginatedResponse } from "./types"

export interface OrganizationQueryParams {
  search?: string
  page: number
  limit: number
  includeDeleted?: boolean
  filters?: { field: string; value: any }[]
  sort?: { field: string; direction: "ASC" | "DESC" }[]
}

export interface OrganizationCreatePayload {
  name: string
  isActive?: boolean
}

export interface OrganizationUpdatePayload {
  name?: string
  isActive?: boolean
}

export class OrganizationsAPI extends BaseAPI {
  static All(params: OrganizationQueryParams) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params),
    ).toString()}`
    return new BaseAPI(`/organizations${queryString}`).build<
      PaginatedResponse<Organization>
    >()
  }

  static Detail = (organizationId: string) =>
    new BaseAPI(`/organizations/${organizationId}`).build<Organization>()

  static Create = (body: OrganizationCreatePayload) =>
    new BaseAPI(`/organizations`, body, "POST").build<Organization>()

  static Update = (organizationId: string, body: OrganizationUpdatePayload) =>
    new BaseAPI(
      `/organizations/${organizationId}`,
      body,
      "PUT",
    ).build<Organization>()

  static Delete = (organizationId: string) =>
    new BaseAPI(`/organizations/${organizationId}`, undefined, "DELETE").build<{
      deleted: boolean
    }>()
}
