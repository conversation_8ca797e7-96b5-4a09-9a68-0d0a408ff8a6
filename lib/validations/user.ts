import { z } from "zod"

export const UserCreateSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  tags: z.array(z.string()),
  isActive: z.boolean().optional().default(true),
  createdBy: z.string().min(1, "Created by is required"),
})

export const UserUpdateSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  email: z.string().email("Invalid email address").optional(),
  tags: z.array(z.string()),
  isActive: z.boolean().optional(),
})

export const UserIdSchema = z.object({
  id: z.string().min(1, "User ID is required"),
})

export type UserCreateInput = z.infer<typeof UserCreateSchema>
export type UserUpdateInput = z.infer<typeof UserUpdateSchema>
export type UserIdInput = z.infer<typeof UserIdSchema>
