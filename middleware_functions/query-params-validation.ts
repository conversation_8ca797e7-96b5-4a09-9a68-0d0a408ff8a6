import { ERROR_CODES } from "@/app/api/error_codes"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { NextRequest, NextResponse } from "next/server"

export function queryValidation(request: NextRequest) {
  const { searchParams } = new URL(request.url)

  // Parse basic parameters
  const search = searchParams.get("search") || undefined
  const includeDeleted = searchParams.get("includeDeleted") === "true"
  const page = searchParams.get("page")
    ? parseInt(searchParams.get("page")!)
    : undefined
  const limit = searchParams.get("limit")
    ? parseInt(searchParams.get("limit")!)
    : undefined

  // Validate pagination parameters
  if (page !== undefined && (isNaN(page) || page < 1)) {
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Page must be a positive integer"],
        [ERROR_CODES.VALIDATION_FAILED],
      ),
      { status: 400 },
    )
  }

  if (limit !== undefined && (isNaN(limit) || limit < 1 || limit > 1000)) {
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Limit must be between 1 and 1000"],
        [ERROR_CODES.VALIDATION_FAILED],
      ),
      { status: 400 },
    )
  }
  return undefined
}
